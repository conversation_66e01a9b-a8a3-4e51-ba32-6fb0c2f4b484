<!DOCTYPE html>
<!DOCTYPE html>
<html oncontextmenu="return false" lang="en">
<head>
    <meta charset="UTF-8">
    <link rel="icon" type="image/x-icon" href="icon.png">
    <!-- <link href="style.css" rel="stylesheet">  Removed link to style.css as requested implicitly by providing all styles inline/in head -->
    <meta name="description" content="Web Developer & Digital Designer delivering innovative framework with a futuristic touch and a stylish interface. Need help with an exciting project? Feel free to shoot me a message.">
    <meta name="keywords" content="<PERSON>, Dev<PERSON><PERSON>, Designer, Portfolio, HTML5, CSS, PHP, JavaScript, Node.js">
    <meta name="author" content="<PERSON>">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON></title>

    <!-- External Libraries & Fonts -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- Removed plugin.js as it likely contained mouse effects -->
    <!-- <script type="text/javascript" src="plugin.js"></script> -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Pacifico&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/remixicon/4.6.0/remixicon.min.css" rel="stylesheet">
    <!-- Added tsparticles library -->
    <script src="https://cdn.jsdelivr.net/npm/tsparticles@2.12.0/tsparticles.bundle.min.js"></script>
    <!-- Added Skycons library -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/skycons/1396634940/skycons.min.js"></script>
    <!-- reCAPTCHA removed to simplify form submission -->

    <!-- Tailwind Configuration -->
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#06020A',
                        secondary: '#4A90E2'
                    },
                    borderRadius: {
                        'none': '0px',
                        'sm': '4px',
                        DEFAULT: '8px',
                        'md': '12px',
                        'lg': '16px',
                        'xl': '20px',
                        '2xl': '24px',
                        '3xl': '32px',
                        'full': '9999px',
                        'button': '8px'
                    }
                }
            }
        }
    </script>

    <!-- Inline Styles -->
    <style>
        /* Base styles */
        body {
            zoom: 0.7; /* Consider if this zoom is truly necessary */
            background-color: black; /* Set explicitly */
            color: white;
            min-height: 100vh;
            overflow-y: auto; /* Changed from hidden for scrolling */

            /* Disable text selection */
            -webkit-user-select: none; /* Safari */
            -moz-user-select: none; /* Firefox */
            -ms-user-select: none; /* IE10+/Edge */
            user-select: none; /* Standard */
        }

        /* Particle Container Styling */
        #tsparticles {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1; /* Ensure particles are behind content */
        }

        /* Loading Screen */
        .loading-screen {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: #000000;
            z-index: 9999;
            display: flex;
            flex-direction: column; /* Adjusted for vertical layout */
            justify-content: center;
            align-items: center;
            transition: opacity 0.5s ease; /* Added transition for fade out */
        }
        .loading-footer {
            position: absolute;
            bottom: 20px;
            text-align: center;
            color: rgba(255, 255, 255, 0.7);
            font-size: 0.9rem;
            width: 100%;
        }
        #loading-bar-container { /* Added a container for better control */
           width: 16rem; /* w-64 */
           height: 0.5rem; /* h-2 */
           background-color: #4b5563; /* bg-gray-700 or similar */
           border-radius: 9999px; /* rounded-full */
           overflow: hidden;
        }
        #loading-bar {
            height: 100%;
            width: 0;
            background-color: white;
            transition: width 0.1s linear;
        }
        #loading-percentage {
            margin-top: 1rem; /* mt-4 */
            color: white;
        }

        /* Clock */
        .clock {
            position: fixed;
            top: 20px;
            left: 20px;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            padding: 10px 20px;
            border-radius: 15px;
            color: white;
            font-size: 1.2rem;
            z-index: 1000;
            display: flex;
            gap: 15px;
        }

        /* Weather Widget & Toggle */
        .weather-toggle {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1001;
            transition: transform 0.3s ease;
            background: rgba(255, 255, 255, 0.1); /* Applied consistent style */
            padding: 0.75rem; /* p-3 */
            border-radius: 9999px; /* rounded-full */
            cursor: pointer;
        }
        .weather-toggle:hover {
            transform: scale(1.1);
            background: rgba(255, 255, 255, 0.2); /* hover:bg-white/20 */
        }

        /* Spotify Toggle Button */
        .spotify-toggle {
            position: fixed;
            bottom: 20px;
            left: 20px;
            z-index: 1001;
            transition: transform 0.3s ease;
            background: rgba(30, 215, 96, 0.2); /* Spotify green with transparency */
            padding: 0.75rem; /* p-3 */
            border-radius: 9999px; /* rounded-full */
            cursor: pointer;
            color: white; /* Ensure icon is visible */
            border: none; /* Remove default button border */
        }
        .spotify-toggle:hover {
            transform: scale(1.1);
            background: rgba(30, 215, 96, 0.3); /* Brighter on hover */
        }
        .weather-widget {
            position: fixed;
            top: 80px; /* Adjusted position to not overlap toggle */
            right: 20px;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 15px;
            color: white;
            /* display: none; */ /* Let visibility handle hiding */
            z-index: 1000;
            transform: scale(0.95); /* Start slightly smaller */
            opacity: 0; /* Start invisible */
            visibility: hidden; /* Start hidden */
            /* Updated transition for smoother fade and scale */
            transition: transform 0.3s ease-out, opacity 0.3s ease-out, visibility 0s 0.3s;
            width: 250px;
            transform-origin: top right; /* Scale originates from the corner */
        }

        /* Spotify Widget */
        .spotify-widget {
            position: fixed;
            bottom: 80px; /* Position above the toggle button */
            left: 20px;
            background: rgba(255, 255, 255, 0.1); /* Match weather widget's transparent background */
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 15px;
            color: white;
            z-index: 1000;
            transform: scale(0.95); /* Start slightly smaller */
            opacity: 0; /* Start invisible */
            visibility: hidden; /* Start hidden */
            transition: transform 0.3s ease-out, opacity 0.3s ease-out, visibility 0s 0.3s;
            width: 300px; /* Wider for the playlist */
            transform-origin: bottom left; /* Scale originates from the corner */
            border: 1px solid rgba(30, 215, 96, 0.3); /* Subtle Spotify green border */
        }
        .weather-widget.active, .spotify-widget.active {
            transform: scale(1);
            opacity: 1; /* Become visible */
            visibility: visible; /* Become visible */
            /* Updated transition timing for entry */
            transition: transform 0.3s ease-out, opacity 0.3s ease-out, visibility 0s 0s;
        }
        .forecast-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 8px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        .forecast-item:last-child {
            border-bottom: none;
        }

        /* Profile Picture Border & Orbs */
         .profile-border {
             position: relative;
             width: 224px; /* w-56 */
             height: 224px; /* h-56 */
             margin-left: auto;
             margin-right: auto;
             margin-bottom: 1rem; /* mb-4 */
         }
        .profile-border::before,
        .profile-border::after {
            content: '';
            position: absolute;
            top: -5px;
            left: -5px;
            right: -5px;
            bottom: -5px;
            border-radius: 50%;
            background: conic-gradient(from 0deg, transparent, rgba(255, 255, 255, 0.8), transparent, rgba(255, 255, 255, 0.4), transparent);
            animation: rotate 4s linear infinite;
            z-index: 5; /* Ensure border is above image but below orbs if needed */
        }
        .profile-border::after {
            background: conic-gradient(from 180deg, transparent, rgba(255, 255, 255, 0.4), transparent, rgba(255, 255, 255, 0.8), transparent);
            animation-direction: reverse; /* Use animation-direction */
            filter: blur(5px);
        }
        @keyframes rotate {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .orb {
            position: absolute;
            width: 10px;
            height: 10px;
            background: white;
            border-radius: 50%;
            filter: blur(3px);
            opacity: 0.7;
            z-index: 15; /* Above profile image and border */
            /* Animation handled by JS */
        }
        #orbs-container { /* Container needed for orb positioning */
            position: absolute;
            top: 0; left: 0; width: 100%; height: 100%;
        }
        .profile-border img {
            position: relative; /* Needed for z-index stacking */
            z-index: 10;
            border-radius: 50%; /* rounded-full */
            width: 100%;
            height: 100%;
            object-fit: cover; /* object-cover */
        }

        /* Project Card Effects */
        .border-effect {
            animation: borderRotate 4s linear infinite;
            position: absolute;
            inset: 0;
            border-width: 2px; /* border-2 */
            border-color: transparent; /* border-transparent */
            /* background applied via Tailwind */
            z-index: 0; /* Behind content */
            border-radius: 0.5rem; /* Match parent rounded-lg */
        }
        @keyframes borderRotate {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .project-card {
            transition: transform 0.3s ease;
            position: relative; /* Needed for absolute positioning of border-effect */
            overflow: hidden; /* Clip the rotating border */
            background-color: rgba(255, 255, 255, 0.05); /* bg-white/5 */
            border-radius: 0.5rem; /* rounded-lg */
            padding: 1rem; /* p-4 */
            cursor: pointer;
        }
        .project-card:hover {
            transform: translateY(-5px);
            background-color: rgba(255, 255, 255, 0.1); /* hover:bg-white/10 */
        }
        .project-card > * { /* Ensure content is above the border effect */
            position: relative;
            z-index: 1;
        }
        .project-card img {
            border-radius: 0.25rem; /* rounded (slightly smaller than card) */
            margin-bottom: 1rem; /* mb-4 */
        }

        /* Service Card Effects */
        .service-card { /* Added class for easier targeting */
            background-color: rgba(255, 255, 255, 0.05); /* bg-white/5 */
            padding: 1.5rem; /* p-6 */
            border-radius: 0.5rem; /* rounded-lg */
            position: relative;
            overflow: hidden;
            cursor: pointer;
            transition: all 0.3s ease; /* transition-all duration-300 */
        }
        .service-card:hover {
            transform: scale(1.05); /* hover:scale-105 */
            background-color: rgba(255, 255, 255, 0.1); /* hover:bg-white/10 */
        }
        .service-card-border { /* Renamed for clarity */
             position: absolute;
             inset: 0;
             border-width: 2px; /* border-2 */
             border-color: transparent; /* border-transparent */
             opacity: 0.2; /* opacity-20 */
             /* Background gradient applied via Tailwind */
             z-index: 0;
             border-radius: 0.5rem; /* Match parent */
        }
        .service-card > * { /* Ensure content is above the border effect */
             position: relative;
             z-index: 1;
        }

        /* Utility: Gradient Text/Animation (if needed elsewhere) */
        @keyframes gradient {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        /* RemixIcon fix (if needed, though usually handled by framework) */
        :where([class^="ri-"])::before { /* content: "\f3c2"; */ } /* Content might vary, check RemixIcon usage */

        /* Contact Toggle Button */
        .contact-toggle {
            position: fixed;
            bottom: 20px;
            right: 20px;
            z-index: 1001;
            transition: transform 0.3s ease;
            background: rgba(255, 255, 255, 0.1);
            padding: 0.75rem; /* p-3 */
            border-radius: 9999px; /* rounded-full */
            cursor: pointer;
            color: white; /* Ensure icon is visible */
            border: none; /* Remove default button border */
        }
        .contact-toggle:hover {
            transform: scale(1.1);
            background: rgba(255, 255, 255, 0.2); /* hover:bg-white/20 */
        }

        /* Contact Form Overlay */
        .contact-overlay {
            position: fixed;
            inset: 0; /* top, right, bottom, left = 0 */
            background: rgba(0, 0, 0, 0.8); /* Semi-transparent black background */
            backdrop-filter: blur(10px);
            z-index: 5000; /* High z-index to cover everything */
            display: flex;
            justify-content: center;
            align-items: center;
            opacity: 0;
            visibility: hidden; /* Use visibility for better accessibility and performance */
            transform: scale(0.95);
            transition: opacity 0.4s cubic-bezier(0.165, 0.84, 0.44, 1), transform 0.4s cubic-bezier(0.165, 0.84, 0.44, 1), visibility 0s 0.4s; /* Delay visibility transition */
        }
        .contact-overlay.active {
            opacity: 1;
            visibility: visible;
            transform: scale(1);
            transition: opacity 0.4s cubic-bezier(0.165, 0.84, 0.44, 1), transform 0.4s cubic-bezier(0.165, 0.84, 0.44, 1), visibility 0s 0s;
        }

        /* Contact Form Container */
        .contact-form-container {
            background: rgba(255, 255, 255, 0.05); /* bg-white/5 */
            padding: 2rem; /* p-8 */
            border-radius: 0.5rem; /* rounded-lg */
            width: 90%;
            max-width: 500px; /* Limit max width */
            position: relative;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        }

        /* Contact Form Close Button */
        .contact-close-button {
            position: absolute;
            top: 1rem; /* p-4 */
            right: 1rem; /* p-4 */
            background: none;
            border: none;
            color: rgba(255, 255, 255, 0.7);
            cursor: pointer;
            font-size: 1.5rem; /* ri-xl */
            transition: color 0.3s ease, transform 0.3s ease;
        }
        .contact-close-button:hover {
            color: white;
            transform: rotate(90deg);
        }

        /* Form Elements Styling (using Tailwind utilities where possible) */
        .contact-form-container input[type="text"],
        .contact-form-container input[type="email"],
        .contact-form-container textarea {
            background-color: rgba(255, 255, 255, 0.1); /* bg-white/10 */
            border: 1px solid rgba(255, 255, 255, 0.2); /* border border-white/20 */
            color: white;
            padding: 0.75rem 1rem; /* py-3 px-4 */
            border-radius: 0.375rem; /* rounded-md */
            width: 100%;
            margin-bottom: 1rem; /* mb-4 */
            transition: border-color 0.3s ease, box-shadow 0.3s ease;
        }
        .contact-form-container input[type="text"]:focus,
        .contact-form-container input[type="email"]:focus,
        .contact-form-container textarea:focus {
            outline: none;
            border-color: rgba(74, 144, 226, 0.8); /* secondary color */
            box-shadow: 0 0 0 3px rgba(74, 144, 226, 0.3); /* Ring effect */
        }
         .contact-form-container textarea {
            min-height: 120px; /* Adjust as needed */
            resize: vertical;
         }
         .contact-form-container button[type="submit"] {
            background-color: #4A90E2; /* secondary */
            color: white;
            padding: 0.75rem 1.5rem; /* py-3 px-6 */
            border-radius: 0.375rem; /* rounded-md */
            border: none;
            cursor: pointer;
            transition: background-color 0.3s ease, transform 0.2s ease;
            font-weight: bold;
         }
         .contact-form-container button[type="submit"]:hover {
            background-color: #3a7ac2; /* Darker shade */
            transform: translateY(-2px);
         }
         /* Placeholder text color */
        .contact-form-container ::placeholder {
            color: rgba(255, 255, 255, 0.5);
            opacity: 1; /* Firefox */
        }
         .contact-form-container :-ms-input-placeholder { /* Internet Explorer 10-11 */
           color: rgba(255, 255, 255, 0.5);
         }
         .contact-form-container ::-ms-input-placeholder { /* Microsoft Edge */
           color: rgba(255, 255, 255, 0.5);
         }

         /* Form result message */
         #form-result {
            margin-top: 1rem; /* mt-4 */
            min-height: 1.5rem; /* Ensure space is reserved */
            text-align: center;
         }

         /* Built With Badge */
        .built-with-badge {
            position: fixed;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(5px); /* Consistent blur */
            padding: 8px 10px; /* Keep padding reasonable */
            border-radius: 15px; /* Match clock style */
            font-size: 0.8rem; /* Slightly smaller text */
            color: rgba(255, 255, 255, 0.8);
            z-index: 1000; /* Same level as clock */
            overflow: hidden; /* Contain the shine effect */
            position: relative; /* For pseudo-element positioning */
            border: 1px solid rgba(255, 255, 255, 0.15); /* Subtle border */
            /* Added max-width and text-align */
            max-width: 160px; /* Adjust value as needed */
            text-align: center; /* Center text if it wraps */
        }

        /* Shiny Effect */
        .built-with-badge::after {
            content: '';
            position: absolute;
            top: -10%; /* Extend slightly above/below */
            left: -150%; /* Start far left */
            width: 75%; /* Width of the shine */
            height: 120%; /* Extend slightly above/below */
            background: linear-gradient(
                90deg,
                rgba(255, 255, 255, 0) 0%,
                rgba(255, 255, 255, 0.2) 50%,
                rgba(255, 255, 255, 0) 100%
            );
            transform: skewX(-25deg); /* Angle the shine */
            animation: badge-shine 5s linear infinite;
            animation-delay: 1s; /* Start after a small delay */
        }

        @keyframes badge-shine {
            0% {
                left: -150%;
            }
            100% {
                left: 150%; /* Move across the badge */
            }
        }

        /* Map Section */
        .map-section {
            position: relative;
            height: 300px;
            overflow: hidden;
        }
        .map-container {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
        }
        .map-container iframe {
            width: 100%;
            height: 100%;
            border: none;
        }

        /* Map Section Styling */
        .map-section h2 {
            /* Optional: Add specific styling or keep Tailwind classes */
        }

        /* Technology Icon Tooltips */
        .tech-icon-container {
            position: relative;
            display: inline-flex;
            justify-content: center;
            align-items: center;
        }
        .tech-tooltip {
            position: absolute;
            bottom: -25px;
            left: 50%;
            transform: translateX(-50%);
            background-color: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.75rem;
            white-space: nowrap;
            opacity: 0;
            visibility: hidden;
            transition: opacity 0.2s, visibility 0.2s;
            pointer-events: none;
            z-index: 10;
        }
        .tech-icon-container:hover .tech-tooltip {
            opacity: 1;
            visibility: visible;
        }
        .map-container {
          position: relative;
          overflow: hidden; /* Ensures content fits rounded corners */
          width: 100%;
          padding-top: 50%; /* Aspect Ratio (height/width * 100). Adjust 50% for desired height */
          border-radius: 0.5rem; /* rounded-lg */
          background-color: #2d2d2d; /* Dark background placeholder */
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4); /* Subtle shadow */
        }
        .map-container iframe {
          position: absolute;
          top: 0;
          left: 0;
          bottom: 0;
          right: 0;
          width: 100%;
          height: 100%;
          border: none; /* Remove default iframe border */
          /* CSS Filter for Dark Mode - adjust values as needed */
          filter: invert(100%) hue-rotate(180deg) contrast(85%) brightness(1.1);
        }

    </style>
</head>

<body>

    <!-- Loading Screen -->
    <div id="loading-screen" class="loading-screen">
        <div class="text-4xl mb-8 animate-bounce">
            <i class="ri-code-line text-white"></i>
        </div>
        <div id="loading-bar-container">
           <div id="loading-bar"></div>
        </div>
        <div id="loading-percentage">0%</div>
        <div class="loading-footer">Built using <b><a href="https://www.nextstarter.xyz/">NextJS</a></b> and <b><a href="https://code.visualstudio.com/">VSCode</b></a></b></div>
    </div>

    <!-- Particle Background Container -->
    <div id="tsparticles"></div>

    <!-- Fixed UI Elements -->
    <div class="clock">
        <span id="time"></span>
        <span id="date"></span>
    </div>

    <button class="weather-toggle">
        <i class="ri-cloud-line ri-lg"></i>
    </button>

    <!-- Add Contact Toggle Button -->
    <button class="contact-toggle" id="contact-toggle-button" title="Contact Me">
        <i class="ri-at-line ri-lg"></i>
    </button>

    <!-- Spotify Toggle Button -->
    <button class="spotify-toggle" id="spotify-toggle-button" title="My Playlist">
        <i class="ri-spotify-line ri-lg"></i>
    </button>

    <!-- Spotify Widget -->
    <div class="spotify-widget" id="spotify-widget">
        <div class="text-xl font-bold mb-3 flex items-center">
            <i class="ri-spotify-line mr-2"></i> My Playlist
        </div>
        <iframe src="https://open.spotify.com/embed/playlist/6Tjt6XXbRBwSw5OTowp7NL?utm_source=generator"
               width="100%" height="380" frameborder="0" allowtransparency="true"
               allow="encrypted-media" loading="lazy">
        </iframe>
    </div>

    <div class="weather-widget">
        <div class="text-xl font-bold mb-2">Montreal</div>
        <div class="weather-info"></div>
        <div class="mt-4">
            <h3 class="text-sm font-semibold mb-2">Forecast</h3>
            <div class="forecast-container"></div>
        </div>
    </div>

    <!-- Main Content Area -->
    <div class="container mx-auto px-4 py-12 relative z-10">

        <!-- Header Section -->
        <header class="text-center mb-16">
            <div class="profile-border" id="profile-container">
                <img src="http://uzy.rf.gd/yusuf.jpg" alt="Profile">
                 <div id="orbs-container"></div>
            </div>
            <h1 class="text-5xl font-bold mb-4">Yusuf</h1>
            <div class="flex items-center justify-center gap-2 text-base text-gray-300 mb-8">
                <span>Web Developer & Designer</span>
                <span class="flex items-center justify-center">
                    <span class="w-2 h-2 bg-green-500 rounded-full animate-pulse"></span>
                </span>
            </div>
            <p class="text-lg text-gray-300 mb-8">Learning, building and gaining skills with every click.</p>
            <div class="flex justify-center gap-6">
                <a href="mailto:<EMAIL>" class="hover:text-gray-300 transition-all duration-300 hover:scale-125"><i class="ri-mail-line ri-lg"></i></a>
                <a href="https://www.instagram.com/uzygram" target="_blank" rel="noopener noreferrer" class="hover:text-gray-300 transition-all duration-300 hover:scale-125"><i class="ri-instagram-line ri-lg"></i></a>
                <a href="https://github.com/uzygram" target="_blank" rel="noopener noreferrer" class="hover:text-gray-300 transition-all duration-300 hover:scale-125"><i class="ri-github-line ri-lg"></i></a>
                <a href="https://linktr.ee/uzymtl" target="_blank" rel="noopener noreferrer" class="hover:text-gray-300 transition-all duration-300 hover:scale-125"><i class="ri-tree-line ri-lg"></i></a>
                <a href="http://uzy.rf.gd/target.php" target="_blank" rel="noopener noreferrer" class="hover:text-gray-300 transition-all duration-300 hover:scale-125"><i class="ri-linkedin-line ri-lg"></i></a>
            </div>
            <div class="mt-16">
                <h3 class="text-lg text-gray-300 mb-4 text-center"><b>Experiences with</b></h3>
                <!-- Tooltip styles moved to head section -->

                <div class="flex justify-center gap-6 flex-wrap">
                    <div class="tech-icon-container">
                        <i class="ri-html5-line ri-2x text-gray-400 transition-all duration-300 hover:text-white hover:scale-125 hover:drop-shadow-[0_0_15px_rgba(255,255,255,0.7)]"></i>
                        <span class="tech-tooltip">HTML5</span>
                    </div>
                    <div class="tech-icon-container">
                        <i class="ri-css3-line ri-2x text-gray-400 transition-all duration-300 hover:text-white hover:scale-125 hover:drop-shadow-[0_0_15px_rgba(255,255,255,0.7)]"></i>
                        <span class="tech-tooltip">CSS3</span>
                    </div>
                    <div class="tech-icon-container">
                        <i class="ri-tailwind-css-line ri-2x text-gray-400 transition-all duration-300 hover:text-white hover:scale-125 hover:drop-shadow-[0_0_15px_rgba(255,255,255,0.7)]"></i>
                        <span class="tech-tooltip">Tailwind CSS</span>
                    </div>
                    <div class="tech-icon-container">
                        <i class="ri-javascript-line ri-2x text-gray-400 transition-all duration-300 hover:text-white hover:scale-125 hover:drop-shadow-[0_0_15px_rgba(255,255,255,0.7)]"></i>
                        <span class="tech-tooltip">JavaScript</span>
                    </div>
                    <div class="tech-icon-container">
                        <i class="ri-reactjs-line ri-2x text-gray-400 transition-all duration-300 hover:text-white hover:scale-125 hover:drop-shadow-[0_0_15px_rgba(255,255,255,0.7)]"></i>
                        <span class="tech-tooltip">React</span>
                    </div>
                    <div class="tech-icon-container">
                        <i class="ri-nextjs-line ri-2x text-gray-400 transition-all duration-300 hover:text-white hover:scale-125 hover:drop-shadow-[0_0_15px_rgba(255,255,255,0.7)]"></i>
                        <span class="tech-tooltip">Next.js</span>
                    </div>
                    <div class="tech-icon-container">
                        <i class="ri-vuejs-line ri-2x text-gray-400 transition-all duration-300 hover:text-white hover:scale-125 hover:drop-shadow-[0_0_15px_rgba(255,255,255,0.7)]"></i>
                        <span class="tech-tooltip">Vue.js</span>
                    </div>
                    <div class="tech-icon-container">
                        <i class="ri-nodejs-line ri-2x text-gray-400 transition-all duration-300 hover:text-white hover:scale-125 hover:drop-shadow-[0_0_15px_rgba(255,255,255,0.7)]"></i>
                        <span class="tech-tooltip">Node.js</span>
                    </div>
                </div>
            </div>
        </header>

        <!-- Projects Section -->
        <section class="mb-16">
            <h2 class="text-2xl font-bold mb-8">+ MY SERVICES</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div class="project-card">
                    <div class="border-effect bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500"></div>
                    <img src="http://uzy.rf.gd/ysf.jpg" alt="Project 1" class="w-full h-48 object-cover">
                    <h3 class="text-xl font-bold mb-2">WEB DEVELOPMENT 🪐</h3>
                    <p class="text-gray-500">Specialized in creating responsive websites that deliver exceptional user experiences.</p>
                </div>
                <div class="project-card">
                    <div class="border-effect bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500"></div>
                    <img src="http://uzy.rf.gd/bg.jpg" alt="Project 2" class="w-full h-48 object-cover">
                    <h3 class="text-xl font-bold mb-2">UI/UX ✨</h3>
                    <p class="text-gray-500">Addicted to clean UI, bringing clean and intuitive user interfaces to reality with a redefined design.</p>
                </div>
            </div>
        </section>

        <!-- Services Section -->
        <section class="mb-16 mb-24">
            <h2 class="text-2xl font-bold mb-8">+ MY TOOLS</h2>
            <div class="space-y-6">
                 <div class="service-card">
                    <div class="service-card-border bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500"></div>
                    <div class="flex items-center gap-4 mb-2">
                        <i class="ri-code-s-slash-line ri-lg"></i>
                        <h3 class="text-xl font-bold">Visual Studio Code</h3>
                    </div>
                    <p class="text-gray-500">VSCode is my primary environment, providing unmatched flexibility that optimizes my coding experience for maximum productivity and comfort.</p>
                </div>
                 <div class="service-card">
                     <div class="service-card-border bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500"></div>
                    <div class="flex items-center gap-4 mb-2">
                        <i class="ri-vercel-line ri-lg"></i>
                        <h3 class="text-xl font-bold">Vercel</h3>
                    </div>
                    <p class="text-gray-500">My preferred platform for fast, reliable deployments and modern frontend frameworks.</p>
                </div>
                 <div class="service-card">
                     <div class="service-card-border bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500"></div>
                    <div class="flex items-center gap-4 mb-2">
                        <i class="ri-copilot-fill ri-lg"></i>
                        <h3 class="text-xl font-bold">GitHub</h3>
                    </div>
                    <p class="text-gray-500">GitHub serves as the backbone of my development process. With its community and collaborative capabilities, it's an indispensable tool in my ecosystem.</p>
                </div>
            </div>
        </section>

        <!-- Map Section -->
        <section class="mb-16 map-section">
             <h2 class="text-2xl font-bold mb-8">+ LOCATION</h2>
             <div class="map-container">
                 <iframe
                    src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d89466.98000211321!2d-73.71074746267145!3d45.51323649371029!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x4cc91a4d31166b3d%3A0xe16252d7fe06209e!2sVille-Marie%2C%20Montreal%2C%20QC!5e0!3m2!1sen!2sca!4v1745422664417!5m2!1sen!2sca"
                    allowfullscreen=""
                    loading="lazy"
                    referrerpolicy="no-referrer-when-downgrade"
                    title="Map of Montreal">
                 </iframe>
             </div>
        </section>

    </div>

    <!-- Contact Form Overlay -->
    <div class="contact-overlay" id="contact-overlay">
        <div class="contact-form-container">
            <button class="contact-close-button" id="contact-close-button" title="Close">
                <i class="ri-close-line"></i>
            </button>
            <h2 class="text-2xl font-bold mb-2 text-center">Let's work together!</h2>
            <p class="text-base text-gray-400 mb-6 text-center">
                Have an exciting project in mind or need assistance with? Feel free to shoot me a message — I’d be happy to connect.
            </p>
            <form id="contact-form" action="https://api.web3forms.com/submit" method="POST">
                <!-- Your Web3Forms Access Key -->
                <input type="hidden" name="access_key" value="a922a736-4786-4026-9c1f-97b7a0e06c4e">
                <input type="hidden" name="subject" value="New Contact Form Submission from Portfolio"> <!-- Optional: Default subject -->
                <input type="hidden" name="from_name" value="Yusuf.dev"> <!-- Updated: Identify sender in email -->
                <input type="hidden" name="redirect" value=""> <!-- Optional: Redirect URL on success (JS handles feedback instead) -->
                <!-- Added to bypass domain TLD blocking -->
                <input type="hidden" name="bypass" value="true">
                <!-- Added to help with debugging -->
                <input type="hidden" name="replyto" id="form-email-field">

                <div class="mb-4">
                    <input type="text" name="name" placeholder="Your Name" required>
                </div>
                <div class="mb-4">
                    <input type="email" name="email" placeholder="Your Email" required>
                </div>
                <div class="mb-4">
                    <input type="text" name="project" placeholder="Your Project" required>
                </div>
                <div class="mb-4">
                    <textarea name="message" placeholder="Your Message" required></textarea>
                </div>
                 <!-- Enhanced honeypot fields for spam prevention -->
                 <input type="checkbox" name="botcheck" class="hidden" style="display: none;">
                 <input type="text" name="name_duplicate" style="display: none !important; visibility: hidden; opacity: 0; height: 0; position: absolute; z-index: -1;">
                 <input type="hidden" name="timestamp" id="form-timestamp" value="">
                 <!-- reCAPTCHA removed to simplify form submission -->
                 <!-- Destination email connected to your access key -->
                 <input type="hidden" name="to_email" value="<EMAIL>">

                <div class="text-center">
                    <button type="submit" id="contact-submit-button">Send Message</button>
                </div>
                <div id="form-result" class="mt-4 text-sm"></div> <!-- For feedback -->
            </form>
        </div>
    </div>

    <!-- Built With Badge -->
    <div class="built-with-badge text-base">
        <b>© 2025 Yusuf K.</b>
    </div>

    <!-- JavaScript -->
    <script>
        document.addEventListener('DOMContentLoaded', () => {
            // --- Loading Animation ---
            const loadingScreen = document.getElementById('loading-screen');
            const loadingBar = document.getElementById('loading-bar');
            const loadingPercentage = document.getElementById('loading-percentage');
            let progress = 0;
            const loadingInterval = setInterval(() => {
                progress += 1;
                // Ensure progress doesn't exceed 100 visually if interval runs fast
                const displayProgress = Math.min(progress, 100);
                loadingBar.style.width = `${displayProgress}%`;
                loadingPercentage.textContent = `${displayProgress}%`;

                if (progress >= 100) {
                    clearInterval(loadingInterval);
                    setTimeout(() => {
                        loadingScreen.style.opacity = '0';
                        // Use transitionend event for more reliable removal
                        loadingScreen.addEventListener('transitionend', () => {
                           if (loadingScreen.style.opacity === '0') { // Check final state
                             loadingScreen.style.display = 'none';
                           }
                        }, { once: true }); // Remove listener after it runs once
                    }, 300); // Wait a bit after 100%
                }
            }, 30); // Loading speed

            // --- Particle Background ---
            tsParticles.load("tsparticles", {
                fpsLimit: 60,
                particles: {
                    number: {
                        value: 80, // Adjust particle count
                        density: {
                            enable: true,
                            value_area: 800
                        }
                    },
                    color: {
                        value: "#ffffff" // Particle color
                    },
                    shape: {
                        type: "circle"
                    },
                    opacity: {
                        value: 0.5, // Particle opacity
                        random: true,
                        anim: {
                            enable: true,
                            speed: 0.5,
                            opacity_min: 0.1,
                            sync: false
                        }
                    },
                    size: {
                        value: 2, // Particle size
                        random: true,
                        anim: {
                            enable: false
                        }
                    },
                    line_linked: {
                        enable: false // Disable lines connecting particles
                    },
                    move: {
                        enable: true,
                        speed: 0.5, // Particle speed
                        direction: "none",
                        random: true,
                        straight: false,
                        out_mode: "out",
                        bounce: false,
                        attract: {
                            enable: false
                        }
                    }
                },
                interactivity: {
                    detect_on: "canvas",
                    events: {
                        onhover: {
                            enable: false, // Disable hover effects
                        },
                        onclick: {
                            enable: false, // Disable click effects
                        },
                        resize: true
                    }
                },
                detectRetina: true,
                background: {
                   color: "#000000", // Ensure background matches body
                }
            });


            // --- Profile Orbs ---
            function createProfileOrbs() {
                const orbsContainer = document.getElementById('orbs-container');
                const profileContainer = document.getElementById('profile-container');
                if (!orbsContainer || !profileContainer) return; // Safety check

                const radius = profileContainer.offsetWidth / 2;

                // Clear existing orbs if function is called again
                orbsContainer.innerHTML = '';

                for (let i = 0; i < 3; i++) { // Create 3 orbs
                    const orb = document.createElement('div');
                    orb.className = 'orb';

                    // Set initial position and animation properties (could also be done via JS animation)
                    const angle = Math.random() * Math.PI * 2;
                    const orbRadius = radius + 5 + Math.random() * 5; // Slightly varied distance
                    const x = Math.cos(angle) * orbRadius;
                    const y = Math.sin(angle) * orbRadius;

                    orb.style.left = `calc(50% + ${x}px - 5px)`; // Adjust for orb size
                    orb.style.top = `calc(50% + ${y}px - 5px)`; // Adjust for orb size

                    orbsContainer.appendChild(orb);
                    animateOrb(orb, radius + 5 + Math.random() * 5); // Use slightly varied radius for movement
                }
            }

            function animateOrb(orb, orbitRadius) {
                let angle = Math.random() * Math.PI * 2; // Start angle
                const speed = (0.001 + Math.random() * 0.002) * (Math.random() > 0.5 ? 1 : -1); // Random speed and direction

                function updatePosition() {
                    angle += speed;
                    const x = Math.cos(angle) * orbitRadius;
                    const y = Math.sin(angle) * orbitRadius;

                    // Center calculation assumes orb's transform-origin is center
                    orb.style.transform = `translate(${x}px, ${y}px)`;

                    // Check if orb still exists in DOM before requesting next frame
                    if (document.body.contains(orb)) {
                       requestAnimationFrame(updatePosition);
                    }
                }
                // Initial positioning before starting animation loop
                const initialX = Math.cos(angle) * orbitRadius;
                const initialY = Math.sin(angle) * orbitRadius;
                orb.style.left = `calc(50% - 5px)`; // Center horizontally before translate
                orb.style.top = `calc(50% - 5px)`;  // Center vertically before translate
                orb.style.transform = `translate(${initialX}px, ${initialY}px)`;

                requestAnimationFrame(updatePosition);
            }


            // --- Clock and Date ---
            function updateClock() {
                const now = new Date();
                const timeElement = document.getElementById('time');
                const dateElement = document.getElementById('date');

                if (timeElement && dateElement) { // Check if elements exist
                    // Format time: HH:MM:SS (24-hour)
                    const time = now.toLocaleTimeString('en-GB'); // 'en-GB' often gives HH:MM:SS

                    // Format date: ShortWeekday, ShortMonth Day
                    const date = now.toLocaleDateString('en-US', {
                        weekday: 'short',
                        month: 'short',
                        day: 'numeric'
                    });

                    timeElement.textContent = time;
                    dateElement.textContent = date;
                }
            }
            setInterval(updateClock, 1000);
            updateClock(); // Initial call

            // --- Weather Widget ---
            const weatherToggle = document.querySelector('.weather-toggle');
            const weatherWidget = document.querySelector('.weather-widget');
            let weatherVisible = false;
            let weatherCloseTimer = null; // Variable to hold the timeout ID

            // Function to handle closing the widget (to avoid repetition)
            function closeWeatherWidget() {
                if (!weatherVisible) return; // Already closed or closing

                weatherVisible = false;
                if (weatherCloseTimer) {
                    clearTimeout(weatherCloseTimer); // Clear auto-close timer if manually closed
                    weatherCloseTimer = null;
                }
                weatherWidget.classList.remove('active');
                // Listen for transition end to hide the element fully
                weatherWidget.addEventListener('transitionend', () => {
                    if (!weatherWidget.classList.contains('active')) {
                        weatherWidget.style.display = 'none';
                    }
                }, { once: true });
            }

            // --- Spotify Widget ---
            const spotifyToggle = document.getElementById('spotify-toggle-button');
            const spotifyWidget = document.getElementById('spotify-widget');
            let spotifyVisible = false;
            let spotifyCloseTimer = null;

            function closeSpotifyWidget() {
                if (!spotifyVisible) return; // Already closed or closing

                spotifyVisible = false;
                if (spotifyCloseTimer) {
                    clearTimeout(spotifyCloseTimer);
                    spotifyCloseTimer = null;
                }
                spotifyWidget.classList.remove('active');
                // Listen for transition end to hide the element fully
                spotifyWidget.addEventListener('transitionend', () => {
                    if (!spotifyWidget.classList.contains('active')) {
                        // Keep the widget in the DOM but hidden visually
                        // This allows the music to continue playing
                        spotifyWidget.style.visibility = 'hidden';
                        // Don't set display: none to keep the iframe active
                    }
                }, { once: true });
            }

            if (weatherToggle && weatherWidget) { // Check if elements exist
                weatherToggle.addEventListener('click', () => {
                    // Clear any existing timer when toggle is clicked
                    if (weatherCloseTimer) {
                        clearTimeout(weatherCloseTimer);
                        weatherCloseTimer = null;
                    }

                    if (!weatherVisible) { // If opening the widget
                        // Close spotify widget if open
                        if (spotifyVisible) {
                            closeSpotifyWidget();
                        }

                        weatherVisible = true;
                        weatherWidget.style.display = 'block';
                        // Timeout needed to allow display:block before transition starts
                        setTimeout(() => {
                            weatherWidget.classList.add('active');
                        }, 10);
                        fetchWeather(); // Fetch weather when opening

                        // Start the auto-close timer
                        weatherCloseTimer = setTimeout(() => {
                            console.log("Auto-closing weather widget after 4 seconds.");
                            closeWeatherWidget(); // Call the close function
                        }, 4000); // 4000 milliseconds = 4 seconds

                    } else { // If closing the widget manually
                       closeWeatherWidget();
                    }
                });
            }

            if (spotifyToggle && spotifyWidget) {
                spotifyToggle.addEventListener('click', () => {
                    // Clear any existing timer when toggle is clicked
                    if (spotifyCloseTimer) {
                        clearTimeout(spotifyCloseTimer);
                        spotifyCloseTimer = null;
                    }

                    if (!spotifyVisible) { // If opening the widget
                        // Close weather widget if open
                        if (weatherVisible) {
                            closeWeatherWidget();
                        }

                        spotifyVisible = true;
                        // If the widget was just hidden but not removed from DOM (for continuous playback)
                        if (spotifyWidget.style.visibility === 'hidden') {
                            spotifyWidget.style.visibility = 'visible';
                        } else {
                            spotifyWidget.style.display = 'block';
                        }
                        // Timeout needed to allow display:block before transition starts
                        setTimeout(() => {
                            spotifyWidget.classList.add('active');
                        }, 10);

                        // No auto-close for Spotify - let user enjoy the music
                    } else { // If closing the widget manually
                        closeSpotifyWidget();
                    }
                });

                // Close on ESC key (same as contact form)
                document.addEventListener('keydown', (event) => {
                    if (event.key === 'Escape' && spotifyVisible) {
                        closeSpotifyWidget();
                    }
                });

                // Close when clicking anywhere on the document
                document.addEventListener('click', (event) => {
                    // Only close if widget is visible and click is outside the widget and toggle button
                    if (spotifyVisible &&
                        !spotifyWidget.contains(event.target) &&
                        event.target !== spotifyToggle &&
                        !spotifyToggle.contains(event.target)) {
                        closeSpotifyWidget();
                    }
                });
            }

            // Instantiate Skycons (outside fetchWeather to reuse the instance)
            const skycons = new Skycons({"color": "white"});

            async function fetchWeather() {
                const weatherInfo = document.querySelector('.weather-info');
                const forecastContainer = document.querySelector('.forecast-container');
                if (!weatherInfo || !forecastContainer) return;

                const apiKey = 'fb7daab92cf971a98f67b33cf44d3504'; // Replace YOUR_API_KEY
                const city = 'Montreal';
                const units = 'metric';
                const weatherUrl = `https://api.openweathermap.org/data/2.5/weather?q=${city}&units=${units}&appid=${apiKey}`;
                const forecastUrl = `https://api.openweathermap.org/data/2.5/forecast?q=${city}&units=${units}&appid=${apiKey}`;

                // --- Remove Loading Indicator ---
                // Set both areas to blank initially
                weatherInfo.innerHTML = '';
                forecastContainer.innerHTML = '';
                // --- End Loading Indicator Removal ---

                // --- IMPORTANT: Remove previously added Skycons ---
                // Find all canvas elements added by skycons (if any existed) and remove them
                // We know the main ID and the pattern for forecast IDs
                skycons.remove("weather-icon-canvas");
                for (let i = 0; i < 5; i++) { // Check a few potential previous forecast icons
                    skycons.remove(`forecast-icon-${i}`);
                }
                // --- End Removal ---

                try {
                    const [weatherResponse, forecastResponse] = await Promise.all([
                        fetch(weatherUrl),
                        fetch(forecastUrl)
                    ]);

                    if (!weatherResponse.ok) throw new Error(`Weather fetch failed: ${weatherResponse.statusText}`);
                    if (!forecastResponse.ok) throw new Error(`Forecast fetch failed: ${forecastResponse.statusText}`);

                    const weatherData = await weatherResponse.json();
                    const forecastData = await forecastResponse.json();

                    const skyconIdentifier = getSkyconIdentifier(
                        weatherData.weather[0].id,
                        weatherData.dt,
                        weatherData.sys.sunrise,
                        weatherData.sys.sunset
                    );

                    // Update current weather HTML with canvas and increased text sizes
                    weatherInfo.innerHTML = `
                      <div class="flex items-start gap-3 mb-2">
                        <canvas id="weather-icon-canvas" width="48" height="48"></canvas>
                        <div>
                          <div class="flex items-center gap-2 mb-1">
                            <i class="ri-temp-cold-line ri-lg"></i>
                            <span class="text-2xl">${Math.round(weatherData.main.temp)}°C</span>
                          </div>
                          <div class="capitalize text-base">${weatherData.weather[0].description}</div>
                        </div>
                      </div>
                      <div class="flex items-center gap-2 text-base text-gray-300 mb-1">
                        <i class="ri-drop-line"></i> <span>${weatherData.main.humidity}% humidity</span>
                      </div>
                      <div class="flex items-center gap-2 text-base text-gray-300">
                        <i class="ri-windy-line"></i> <span>${Math.round(weatherData.wind.speed * 3.6)} km/h</span>
                      </div>
                    `;
                    // Add the main icon to the Skycons instance
                    skycons.add("weather-icon-canvas", skyconIdentifier);

                    // --- Forecast Update ---
                    const dailyForecasts = {};
                    forecastData.list.forEach(item => {
                        const date = new Date(item.dt * 1000);
                        const dayKey = date.toISOString().split('T')[0]; // YYYY-MM-DD
                        if (!dailyForecasts[dayKey] || Math.abs(date.getHours() - 12) < Math.abs(new Date(dailyForecasts[dayKey].dt * 1000).getHours() - 12)) {
                            if (date.getDate() !== new Date().getDate()) {
                                dailyForecasts[dayKey] = item;
                            }
                        }
                    });
                    const nextThreeDays = Object.values(dailyForecasts).slice(0, 3);

                    // Clear previous forecast container content (already done above)
                    // forecastContainer.innerHTML = ''; // No need to clear again

                    nextThreeDays.forEach((forecast, index) => {
                      const date = new Date(forecast.dt * 1000);
                      const dayName = date.toLocaleDateString('en-US', { weekday: 'short' });
                      const forecastSkyconId = getSkyconIdentifier(forecast.weather[0].id, forecast.dt, weatherData.sys.sunrise, weatherData.sys.sunset);
                      const forecastCanvasId = `forecast-icon-${index}`;

                      const forecastItem = document.createElement('div');
                      forecastItem.className = 'forecast-item items-center';
                      // Update forecast HTML with increased text sizes
                      forecastItem.innerHTML = `
                        <div class="w-1/4 text-left text-base"><span>${dayName}</span></div>
                        <div class="w-1/4 flex justify-center"><canvas id="${forecastCanvasId}" width="24" height="24"></canvas></div>
                        <div class="w-1/4 text-center capitalize text-sm"><span>${forecast.weather[0].description}</span></div>
                        <div class="w-1/4 text-right text-base">
                          <span>${Math.round(forecast.main.temp_max)}°</span> / <span class="text-gray-400">${Math.round(forecast.main.temp_min)}°</span>
                        </div>
                      `;
                      forecastContainer.appendChild(forecastItem);
                      // Add forecast icon to the Skycons instance AFTER appending the element
                      skycons.add(forecastCanvasId, forecastSkyconId);
                    });

                    // --- Play animations AFTER all icons have been added ---
                    skycons.play();

                } catch (error) {
                    console.error('Error fetching weather:', error);
                    // Display error in the main info area if fetch fails
                    weatherInfo.innerHTML = `<div class="text-red-400"><i class="ri-error-warning-line mr-2"></i> Failed to load weather data</div>`;
                    // Clear any potentially added icons on error
                    skycons.remove("weather-icon-canvas");
                     for (let i = 0; i < 3; i++) {
                        skycons.remove(`forecast-icon-${i}`);
                    }
                }
            }

            // Helper function to get Skycon identifier based on weather condition code and time
            function getSkyconIdentifier(code, currentTimeUnix, sunriseUnix, sunsetUnix) {
                const isDay = currentTimeUnix > sunriseUnix && currentTimeUnix < sunsetUnix;

                if (code >= 200 && code < 300) return Skycons.RAIN; // Thunderstorm -> RAIN (or SLEET maybe)
                if (code >= 300 && code < 400) return Skycons.RAIN; // Drizzle -> RAIN
                if (code >= 500 && code < 600) return Skycons.RAIN; // Rain -> RAIN
                if (code >= 600 && code < 700) return Skycons.SNOW; // Snow -> SNOW
                if (code >= 700 && code < 800) return Skycons.FOG;  // Atmosphere -> FOG
                if (code === 800) return isDay ? Skycons.CLEAR_DAY : Skycons.CLEAR_NIGHT; // Clear
                if (code === 801) return isDay ? Skycons.PARTLY_CLOUDY_DAY : Skycons.PARTLY_CLOUDY_NIGHT; // Few clouds
                if (code > 801) return Skycons.CLOUDY; // Scattered, broken, overcast clouds -> CLOUDY

                // Default fallback
                return isDay ? Skycons.CLEAR_DAY : Skycons.CLEAR_NIGHT;
            }

            // --- Contact Form Logic ---
            const contactToggle = document.getElementById('contact-toggle-button');
            const contactOverlay = document.getElementById('contact-overlay');
            const contactClose = document.getElementById('contact-close-button');
            const contactForm = document.getElementById('contact-form');
            const formResult = document.getElementById('form-result');
            const submitButton = document.getElementById('contact-submit-button');

            function openContactForm() {
                contactOverlay.classList.add('active');
                document.body.style.overflow = 'hidden'; // Prevent background scrolling
            }

            function closeContactForm() {
                contactOverlay.classList.remove('active');
                document.body.style.overflow = ''; // Restore background scrolling
                // Clear result message and reset form after animation
                setTimeout(() => {
                    formResult.textContent = '';
                    formResult.className = 'mt-4 text-sm'; // Reset class
                    contactForm.reset();
                    submitButton.disabled = false;
                    submitButton.textContent = 'Send Message';
                }, 400); // Match CSS transition duration
            }

            if (contactToggle && contactOverlay && contactClose && contactForm) {
                contactToggle.addEventListener('click', openContactForm);
                contactClose.addEventListener('click', closeContactForm);

                // Close on clicking background overlay
                contactOverlay.addEventListener('click', (event) => {
                    if (event.target === contactOverlay) { // Ensure click is on overlay itself
                        closeContactForm();
                    }
                });

                // Close on ESC key
                document.addEventListener('keydown', (event) => {
                    if (event.key === 'Escape' && contactOverlay.classList.contains('active')) {
                        closeContactForm();
                    }
                });

                // Handle Form Submission with Fetch API
                contactForm.addEventListener('submit', async function (e) {
                    e.preventDefault(); // Prevent default browser submission
                    submitButton.disabled = true;
                    submitButton.textContent = 'Sending...';
                    formResult.textContent = ''; // Clear previous result
                    formResult.className = 'mt-4 text-sm'; // Reset class

                    const formData = new FormData(contactForm);
                    // Updated to get 'project' instead of 'subject_user'
                    formData.set('subject', `Project: ${formData.get('project')}`); // Set subject using the new 'project' field

                    // Make sure we're using the correct email address
                    formData.set("to_email", "<EMAIL>");

                    // Set timestamp for basic time-based spam detection
                    document.getElementById('form-timestamp').value = Date.now().toString();

                    // Check for rate limiting using localStorage
                    const lastSubmission = localStorage.getItem('lastFormSubmission');
                    const now = Date.now();
                    if (lastSubmission && (now - parseInt(lastSubmission)) < 60000) { // 1 minute cooldown
                        formResult.textContent = 'Please wait a minute before submitting again.';
                        formResult.classList.add('text-yellow-400');
                        submitButton.disabled = false;
                        submitButton.textContent = 'Send Message';
                        return;
                    }

                    try {
                        // Simplified spam checks
                        const name = formData.get('name');
                        const email = formData.get('email');
                        const message = formData.get('message');
                        const honeypotField = formData.get('name_duplicate');

                        // Set reply-to email
                        document.getElementById('form-email-field').value = email;

                        // Only check honeypot field
                        if (honeypotField) {
                            console.log('Honeypot triggered');
                            throw new Error('Bot detected');
                        }

                        console.log('Submitting form to Web3Forms...');

                        // Log form data for debugging
                        for (let pair of formData.entries()) {
                            console.log(pair[0] + ': ' + pair[1]);
                        }

                        const response = await fetch(contactForm.action, {
                            method: 'POST',
                            body: formData,
                            headers: {
                                'Accept': 'application/json' // Request JSON response from Web3Forms
                            }
                        });

                        // Log the raw response for debugging
                        const responseText = await response.text();
                        console.log('Response text:', responseText);

                        // Parse the response as JSON
                        let result;
                        try {
                            result = JSON.parse(responseText);
                        } catch (e) {
                            console.error('Failed to parse response as JSON:', e);
                            throw new Error('Invalid response format');
                        }

                        console.log('Parsed result:', result);

                        if (result && result.success) {
                            // Store submission time for rate limiting
                            localStorage.setItem('lastFormSubmission', Date.now().toString());

                            formResult.textContent = 'Message sent successfully!';
                            formResult.classList.add('text-green-400');
                            setTimeout(closeContactForm, 2000); // Close form after 2s on success
                        } else {
                            console.error("Form submission error:", result);
                            formResult.textContent = `Error: ${result.message || 'Something went wrong.'}`;
                            formResult.classList.add('text-red-400');
                            submitButton.disabled = false; // Re-enable button on error
                            submitButton.textContent = 'Send Message';
                        }
                    } catch (error) {
                        console.error('Network or other error:', error);

                        // Different message for detected spam vs. network errors
                        if (error.message === 'Bot detected' || error.message === 'Suspicious content detected') {
                            formResult.textContent = 'Your submission was flagged as potential spam. Please try again.';
                        } else {
                            formResult.textContent = 'An error occurred. Please try again later.';
                        }

                        formResult.classList.add('text-red-400');
                        submitButton.disabled = false; // Re-enable button on error
                        submitButton.textContent = 'Send Message';
                    }
                });
            }

            // --- Initialize ---
            createProfileOrbs();
            // Resize observer for profile orbs (optional but good for responsiveness)
            const resizeObserver = new ResizeObserver(entries => {
                 // Debounce or throttle this if needed
                 createProfileOrbs();
            });
            const profileContainer = document.getElementById('profile-container');
            if (profileContainer) {
                 resizeObserver.observe(profileContainer);
            }

            // Removed contact form script as there's no form in the HTML
            /*
            document.getElementById('contactForm').addEventListener('submit', function(e) {
                // ... form submission logic ...
            });
            */

        }); // End DOMContentLoaded
    </script>

</body>
</html>