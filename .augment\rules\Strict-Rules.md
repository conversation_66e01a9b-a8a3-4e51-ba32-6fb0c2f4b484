---
type: "always_apply"
---

Always respect these rules when generating or modifying code.

Preserve Existing Functionality
Never break current working code when adding or modifying features. Ensure backward compatibility.

Mobile-First Responsiveness
Always generate responsive code that adapts to mobile, tablet, and desktop without breaking layouts.

Clean & Readable Code
Use consistent indentation (2 or 4 spaces), clear naming conventions, and minimal inline styles. Follow semantic HTML and modern ES standards.

Security Best Practices
Never expose API keys, tokens, or sensitive credentials. Always sanitize input and avoid insecure patterns.

Minimal Dependencies
Only suggest or add libraries/frameworks when truly necessary. Prefer lightweight, native solutions first.

Error-Free & Tested Output
Code must compile/run without syntax errors or broken functionality. Suggest quick manual/auto testing steps.

Documentation & Comments
Add meaningful inline comments and short docstrings for functions/components to explain complex logic.

Performance Awareness
Optimize for fast load times and low memory usage. Avoid unnecessary loops, re-renders, or heavy assets.

Version Control Friendly
Ensure code changes are modular, commit-ready, and easy to review (atomic commits instead of massive dumps).

User Preview Check
After each update, verify in Live Preview (or browser) that both mobile and desktop views remain functional and visually consistent.

Never run destructive commands (rm -rf, format disks, delete system files, kill processes) without explicit human approval. Only execute safe, project-related commands like npm, git, build, or server start.
