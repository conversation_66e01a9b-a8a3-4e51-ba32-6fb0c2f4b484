<!DOCTYPE html>
<!DOCTYPE html>
<html oncontextmenu="return false" lang="en">
<head>
    <meta charset="UTF-8">
    <link rel="icon" type="image/x-icon" href="icon.png">
    <!-- <link href="style.css" rel="stylesheet">  Removed link to style.css as requested implicitly by providing all styles inline/in head -->
    <meta name="description" content="Freelance Web Developer delivering responsive websites with modern technologies. From static front-end pages to full-stack apps — Helping brands grow through effective web solutions. Have an exciting project in mind? Feel free to send me a message, I'd be happy to connect!">
    <meta name="keywords" content="<PERSON>, <PERSON><PERSON>, Freelance, Frontend, Nextjs, Developer, Designer, Portfolio, HTML5, CSS, PHP, JavaScript, Node.js">
    <meta name="author" content="<PERSON>">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON></title>

    <!-- External Libraries & Fonts -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- Removed plugin.js as it likely contained mouse effects -->
    <!-- <script type="text/javascript" src="plugin.js"></script> -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Pacifico&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/remixicon/4.6.0/remixicon.min.css" rel="stylesheet">
    <!-- Added tsparticles library -->
    <script src="https://cdn.jsdelivr.net/npm/tsparticles@2.12.0/tsparticles.bundle.min.js"></script>
    <!-- Added Skycons library -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/skycons/1396634940/skycons.min.js"></script>
    <!-- reCAPTCHA removed to simplify form submission -->

    <!-- Tailwind Configuration -->
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#06020A',
                        secondary: '#4A90E2'
                    },
                    borderRadius: {
                        'none': '0px',
                        'sm': '4px',
                        DEFAULT: '8px',
                        'md': '12px',
                        'lg': '16px',
                        'xl': '20px',
                        '2xl': '24px',
                        '3xl': '32px',
                        'full': '9999px',
                        'button': '8px'
                    }
                }
            }
        }
    </script>

    <!-- Inline Styles -->
    <style>
        /* CSS Variables for Theme Colors */
        :root {
            /* Dark Mode Colors (Default) */
            --bg-color: #000000;
            --text-color: #ffffff;
            --text-color-secondary: rgba(255, 255, 255, 0.7);
            --text-color-muted: rgba(156, 163, 175, 1); /* gray-400 */
            --card-bg: rgba(255, 255, 255, 0.05);
            --card-bg-hover: rgba(255, 255, 255, 0.1);
            --card-border: rgba(255, 255, 255, 0.1);
            --particle-color: #ffffff;
            --shadow-color: rgba(0, 0, 0, 0.4);
            --widget-bg: rgba(255, 255, 255, 0.1);
            --hover-glow: rgba(255, 255, 255, 0.7);

            /* Faster, smoother transition settings for iOS-style switch */
            --theme-transition-duration: 0.3s;
            --theme-transition-timing: cubic-bezier(0.25, 0.1, 0.25, 1);
        }

        /* Light Mode Colors */
        body.light-mode {
            --bg-color: #ffffff;
            --text-color: #000000; /* Changed to pure black for all text */
            --text-color-secondary: #000000; /* Changed to pure black */
            --text-color-muted: #000000; /* Changed to pure black */
            --card-bg: rgba(24, 24, 24, 0.05); /* Updated to use #181818 as base */
            --card-bg-hover: rgba(24, 24, 24, 0.08); /* Updated to use #181818 as base */
            --card-border: rgba(24, 24, 24, 0.15); /* Updated to use #181818 as base */
            --particle-color: #000000; /* Darker particles for better visibility */
            --shadow-color: rgba(24, 24, 24, 0.15); /* Updated to use #181818 as base */
            --widget-bg: rgba(24, 24, 24, 0.08); /* Updated to use #181818 as base */
            --hover-glow: rgba(0, 0, 0, 0.4); /* Updated to match new text color */
        }

        /* Base styles */
        body {
            zoom: 0.7; /* Consider if this zoom is truly necessary */
            background-color: var(--bg-color);
            color: var(--text-color);
            min-height: 100vh;
            overflow-y: auto; /* Changed from hidden for scrolling */
            transition: background-color var(--theme-transition-duration) var(--theme-transition-timing),
                        color var(--theme-transition-duration) var(--theme-transition-timing);

            /* Disable text selection */
            -webkit-user-select: none; /* Safari */
            -moz-user-select: none; /* Firefox */
            -ms-user-select: none; /* IE10+/Edge */
            user-select: none; /* Standard */
        }

        /* Apply smooth transitions to all elements */
        *, *::before, *::after {
            transition: background-color var(--theme-transition-duration) var(--theme-transition-timing),
                        color var(--theme-transition-duration) var(--theme-transition-timing),
                        border-color var(--theme-transition-duration) var(--theme-transition-timing),
                        box-shadow var(--theme-transition-duration) var(--theme-transition-timing);
        }

        /* Smooth theme transition overlay */
        body::after {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: var(--bg-color);
            z-index: 9998;
            opacity: 0;
            pointer-events: none;
            transition: opacity var(--theme-transition-duration) var(--theme-transition-timing);
            backdrop-filter: blur(0px);
        }

        body.theme-transitioning::after {
            opacity: 0.1; /* Even more subtle overlay */
            backdrop-filter: blur(3px); /* Lighter blur effect for faster transitions */
        }

        /* Theme-specific text colors */
        .theme-text-muted {
            color: var(--text-color-muted);
            transition: color 0.5s cubic-bezier(0.4, 0, 0.2, 1);
        }

        /* Ensure all text elements use the correct color in light mode */
        body.light-mode h1,
        body.light-mode h2,
        body.light-mode h3,
        body.light-mode h4,
        body.light-mode h5,
        body.light-mode h6,
        body.light-mode p,
        body.light-mode span,
        body.light-mode div,
        body.light-mode a,
        body.light-mode li {
            color: #000000;
        }

        /* Preserve muted text color for elements with theme-text-muted class */
        body.light-mode .theme-text-muted {
            color: #000000; /* Changed to pure black */
        }

        /* Particle Container Styling */
        #tsparticles {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1; /* Ensure particles are behind content */
        }

        /* iOS-style Theme Toggle Switch */
        .theme-toggle {
            position: fixed;
            top: 20px;
            right: 20px; /* Position at top right corner */
            z-index: 1001;
            cursor: pointer;
            border: none;
            background: transparent;
            padding: 0;
            width: 60px;
            height: 32px;
        }

        /* Switch track */
        .switch-track {
            position: relative;
            width: 60px;
            height: 32px;
            background-color: #222;
            border-radius: 16px;
            transition: background-color 0.3s ease;
            box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
            display: flex;
            align-items: center;
            padding: 0 4px;
        }

        /* Switch handle (circle) */
        .switch-handle {
            position: absolute;
            left: 2px;
            width: 28px;
            height: 28px;
            background-color: #ffffff;
            border-radius: 50%;
            transition: transform 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275),
                        background-color 0.3s ease;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 2;
            /* Make handle slightly transparent to see icons through it */
            opacity: 0.9;
        }

        /* Light mode styles */
        body.light-mode .switch-track {
            background-color: #4cd964; /* iOS green color */
        }

        body.light-mode .switch-handle {
            transform: translateX(28px);
        }

        /* Hover effects */
        .theme-toggle:hover .switch-handle {
            box-shadow: 0 0 8px rgba(255, 255, 255, 0.5);
        }

        body.light-mode .theme-toggle:hover .switch-handle {
            box-shadow: 0 0 8px rgba(76, 217, 100, 0.5);
        }

        /* Active/pressed effect */
        .theme-toggle:active .switch-handle {
            transform: scale(0.9) translateX(0);
        }

        body.light-mode .theme-toggle:active .switch-handle {
            transform: scale(0.9) translateX(28px);
        }

        /* Icons inside the switch - always visible */
        .theme-toggle .icon-moon {
            color: #ffffff;
            font-size: 14px;
            position: absolute;
            left: 8px;
            opacity: 1;
            transition: color 0.3s ease;
            z-index: 3; /* Ensure icon is above the handle */
        }

        .theme-toggle .icon-sun {
            color: rgba(76, 217, 100, 0.5); /* Dimmed in dark mode */
            font-size: 14px;
            position: absolute;
            right: 8px;
            opacity: 1; /* Always visible */
            transition: color 0.3s ease;
            z-index: 3; /* Ensure icon is above the handle */
        }

        /* In light mode, highlight sun and dim moon */
        body.light-mode .theme-toggle .icon-moon {
            color: rgba(255, 255, 255, 0.5); /* Dimmed in light mode */
        }

        body.light-mode .theme-toggle .icon-sun {
            color: #4cd964; /* Bright in light mode */
        }

        /* Loading Screen - Always Dark Mode */
        .loading-screen {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: #000000; /* Hard-coded black for loading screen */
            z-index: 9999;
            display: flex;
            flex-direction: column; /* Adjusted for vertical layout */
            justify-content: center;
            align-items: center;
            transition: opacity 0.5s ease; /* Only transition opacity */
        }
        .loading-footer {
            position: absolute;
            bottom: 20px;
            text-align: center;
            color: rgba(255, 255, 255, 0.7); /* Hard-coded white with opacity */
            font-size: 0.9rem;
            width: 100%;
        }
        #loading-bar-container { /* Added a container for better control */
           width: 18rem; /* Wider container */
           height: 0.75rem; /* Taller container */
           background-color: rgba(255, 255, 255, 0.05); /* Hard-coded for dark mode */
           border-radius: 9999px; /* rounded-full */
           overflow: hidden;
           box-shadow: 0 0 15px rgba(255, 255, 255, 0.1); /* Subtle glow */
        }
        #loading-bar {
            height: 100%;
            width: 0;
            background-color: #ffffff; /* Hard-coded white */
            transition: width 0.1s linear;
        }
        #loading-percentage {
            margin-top: 1rem; /* mt-4 */
            color: #ffffff; /* Hard-coded white */
            font-size: 1.5rem; /* Larger text */
            font-weight: 500; /* Medium weight */
        }

        /* Clock */
        .clock {
            position: fixed;
            top: 20px;
            left: 20px;
            background: var(--widget-bg);
            backdrop-filter: blur(10px);
            padding: 10px 20px;
            border-radius: 15px;
            color: var(--text-color);
            font-size: 1.2rem;
            z-index: 1000;
            display: flex;
            gap: 15px;
            transition: background-color 0.3s ease, color 0.3s ease;
        }

        /* Weather Widget & Toggle */
        .weather-toggle {
            position: fixed;
            top: 80px; /* Position below the clock */
            left: 20px;
            z-index: 1001;
            transition: transform 0.3s ease, background-color 0.3s ease;
            background: var(--widget-bg);
            padding: 0.75rem; /* p-3 */
            border-radius: 9999px; /* rounded-full */
            cursor: pointer;
            color: var(--text-color);
        }
        .weather-toggle:hover {
            transform: scale(1.1);
            background: var(--card-bg-hover);
        }

        /* Spotify Toggle Button */
        .spotify-toggle {
            position: fixed;
            bottom: 20px;
            left: 20px;
            z-index: 1001;
            transition: transform 0.3s ease;
            background: rgba(30, 215, 96, 0.2); /* Spotify green with transparency */
            padding: 0.75rem; /* p-3 */
            border-radius: 9999px; /* rounded-full */
            cursor: pointer;
            color: white; /* Ensure icon is visible */
            border: none; /* Remove default button border */
        }
        .spotify-toggle:hover {
            transform: scale(1.1);
            background: rgba(30, 215, 96, 0.3); /* Brighter on hover */
        }
        .weather-widget {
            position: fixed;
            top: 80px; /* Same top position as weather toggle */
            left: 80px; /* Position to the right of the weather toggle */
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 15px;
            /* display: none; */ /* Let visibility handle hiding */
            z-index: 1000;
            transform: scale(0.95); /* Start slightly smaller */
            opacity: 0; /* Start invisible */
            visibility: hidden; /* Start hidden */
            /* Updated transition for smoother fade and scale */
            transition: transform 0.3s ease-out, opacity 0.3s ease-out, visibility 0s 0.3s,
                        background-color 0.5s cubic-bezier(0.4, 0, 0.2, 1),
                        color 0.5s cubic-bezier(0.4, 0, 0.2, 1),
                        border-color 0.5s cubic-bezier(0.4, 0, 0.2, 1);
            width: 250px;
            transform-origin: top left; /* Scale originates from the left corner */

            /* Dark mode styles (default) */
            background: rgba(0, 0, 0, 0.7);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        /* Light mode styles */
        body.light-mode .weather-widget {
            background: rgba(240, 240, 240, 0.9);
            color: #181818;
            border: 1px solid rgba(24, 24, 24, 0.1);
        }

        /* Spotify Widget */
        .spotify-widget {
            position: fixed;
            bottom: 80px; /* Position above the toggle button */
            left: 20px;
            background: rgba(255, 255, 255, 0.1); /* Match weather widget's transparent background */
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 15px;
            color: white;
            z-index: 1000;
            transform: scale(0.95); /* Start slightly smaller */
            opacity: 0; /* Start invisible */
            visibility: hidden; /* Start hidden */
            transition: transform 0.3s ease-out, opacity 0.3s ease-out, visibility 0s 0.3s;
            width: 300px; /* Wider for the playlist */
            transform-origin: bottom left; /* Scale originates from the corner */
            border: 1px solid rgba(30, 215, 96, 0.3); /* Subtle Spotify green border */
        }
        .weather-widget.active, .spotify-widget.active {
            transform: scale(1);
            opacity: 1; /* Become visible */
            visibility: visible; /* Become visible */
            /* Updated transition timing for entry */
            transition: transform 0.3s ease-out, opacity 0.3s ease-out, visibility 0s 0s;
        }
        .forecast-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 8px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        body.light-mode .forecast-item {
            border-bottom: 1px solid rgba(24, 24, 24, 0.1);
        }
        .forecast-item:last-child {
            border-bottom: none;
        }

        /* Profile Picture Border & Orbs */
         .profile-border {
             position: relative;
             width: 224px; /* w-56 */
             height: 224px; /* h-56 */
             margin-left: auto;
             margin-right: auto;
             margin-bottom: 1rem; /* mb-4 */
         }
        .profile-border::before,
        .profile-border::after {
            content: '';
            position: absolute;
            top: -5px;
            left: -5px;
            right: -5px;
            bottom: -5px;
            border-radius: 50%;
            background: conic-gradient(from 0deg, transparent, rgba(255, 255, 255, 0.8), transparent, rgba(255, 255, 255, 0.4), transparent);
            animation: rotate 4s linear infinite;
            z-index: 5; /* Ensure border is above image but below orbs if needed */
        }
        .profile-border::after {
            background: conic-gradient(from 180deg, transparent, rgba(255, 255, 255, 0.4), transparent, rgba(255, 255, 255, 0.8), transparent);
            animation-direction: reverse; /* Use animation-direction */
            filter: blur(5px);
        }
        @keyframes rotate {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Subtle border for profile in light mode */
        body.light-mode .profile-border::before {
            background: conic-gradient(from 0deg, transparent, rgba(0, 0, 0, 0.3), transparent, rgba(0, 0, 0, 0.2), transparent);
            opacity: 0.5;
        }

        body.light-mode .profile-border::after {
            background: conic-gradient(from 180deg, transparent, rgba(0, 0, 0, 0.2), transparent, rgba(0, 0, 0, 0.3), transparent);
            filter: blur(5px);
            opacity: 0.5;
        }
        /* Orbs removed as requested */
        .profile-border img,
        .profile-border video {
            position: relative; /* Needed for z-index stacking */
            z-index: 10;
            border-radius: 50%; /* rounded-full */
            width: 100%;
            height: 100%;
            object-fit: cover; /* object-cover */
            transition: border 0.3s ease, box-shadow 0.3s ease;
        }

        /* Video-specific styles */
        .profile-border video {
            display: block; /* Remove bottom margin/spacing */
            background-color: transparent; /* Ensure transparency */
            pointer-events: none; /* Prevent interaction with video */
            -webkit-mask-image: -webkit-radial-gradient(white, black); /* Fix for Safari border-radius overflow */
            mask-image: radial-gradient(white, black); /* Standard property for other browsers */
            -webkit-backface-visibility: hidden; /* Prevent flickering in some browsers */
            backface-visibility: hidden;
            transform: translateZ(0); /* Force hardware acceleration */
        }

        /* No outline in light mode */
        body.light-mode .profile-border img,
        body.light-mode .profile-border video {
            border: none;
            box-shadow: none;
        }

        /* Project Card Effects */
        .border-effect {
            animation: borderRotate 4s linear infinite;
            position: absolute;
            inset: 0;
            border-width: 2px; /* border-2 */
            border-color: transparent; /* border-transparent */
            /* background applied via Tailwind */
            z-index: 0; /* Behind content */
            border-radius: 0.5rem; /* Match parent rounded-lg */
        }
        @keyframes borderRotate {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .project-card {
            transition: transform 0.3s ease, background-color 0.3s ease;
            position: relative; /* Needed for absolute positioning of border-effect */
            overflow: hidden; /* Clip the rotating border */
            background-color: var(--card-bg);
            border-radius: 0.5rem; /* rounded-lg */
            padding: 1rem; /* p-4 */
            cursor: pointer;
        }
        .project-card:hover {
            transform: translateY(-5px);
            background-color: var(--card-bg-hover);
        }
        .project-card > * { /* Ensure content is above the border effect */
            position: relative;
            z-index: 1;
        }
        .project-card img {
            border-radius: 0.25rem; /* rounded (slightly smaller than card) */
            margin-bottom: 1rem; /* mb-4 */
        }

        /* Service Card Effects */
        .service-card { /* Added class for easier targeting */
            background-color: var(--card-bg);
            padding: 1.5rem; /* p-6 */
            border-radius: 0.5rem; /* rounded-lg */
            position: relative;
            overflow: hidden;
            cursor: pointer;
            transition: all 0.3s ease; /* transition-all duration-300 */
        }
        .service-card:hover {
            transform: scale(1.05); /* hover:scale-105 */
            background-color: var(--card-bg-hover);
        }
        .service-card-border { /* Renamed for clarity */
             position: absolute;
             inset: 0;
             border-width: 2px; /* border-2 */
             border-color: transparent; /* border-transparent */
             opacity: 0.2; /* opacity-20 */
             /* Background gradient applied via Tailwind */
             z-index: 0;
             border-radius: 0.5rem; /* Match parent */
        }
        .service-card > * { /* Ensure content is above the border effect */
             position: relative;
             z-index: 1;
        }

        /* Utility: Gradient Text/Animation (if needed elsewhere) */
        @keyframes gradient {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        /* RemixIcon color in light/dark mode */
        [class^="ri-"] {
            color: var(--text-color);
            transition: color 0.3s ease;
        }

        /* Ensure icons use the correct color in light mode */
        body.light-mode [class^="ri-"] {
            color: #000000;
        }

        /* Contact Toggle Button */
        .contact-toggle {
            position: fixed;
            bottom: 20px;
            right: 20px;
            z-index: 1001;
            transition: transform 0.3s ease, background-color 0.3s ease;
            background: var(--widget-bg);
            padding: 0.75rem; /* p-3 */
            border-radius: 9999px; /* rounded-full */
            cursor: pointer;
            color: var(--text-color); /* Ensure icon is visible */
            border: none; /* Remove default button border */
        }
        .contact-toggle:hover {
            transform: scale(1.1);
            background: var(--card-bg-hover);
        }

        /* Contact Form Overlay */
        .contact-overlay {
            position: fixed;
            inset: 0; /* top, right, bottom, left = 0 */
            background: rgba(0, 0, 0, 0.8); /* Semi-transparent black background */
            backdrop-filter: blur(10px);
            z-index: 5000; /* High z-index to cover everything */
            display: flex;
            justify-content: center;
            align-items: center;
            opacity: 0;
            visibility: hidden; /* Use visibility for better accessibility and performance */
            transform: scale(0.95);
            transition: opacity 0.4s cubic-bezier(0.165, 0.84, 0.44, 1), transform 0.4s cubic-bezier(0.165, 0.84, 0.44, 1), visibility 0s 0.4s; /* Delay visibility transition */
        }
        .contact-overlay.active {
            opacity: 1;
            visibility: visible;
            transform: scale(1);
            transition: opacity 0.4s cubic-bezier(0.165, 0.84, 0.44, 1), transform 0.4s cubic-bezier(0.165, 0.84, 0.44, 1), visibility 0s 0s;
        }

        /* Contact Form Container */
        .contact-form-container {
            padding: 2rem; /* p-8 */
            border-radius: 0.5rem; /* rounded-lg */
            width: 90%;
            max-width: 500px; /* Limit max width */
            position: relative;
            box-shadow: 0 10px 30px var(--shadow-color);
            transition: background-color 0.3s ease, box-shadow 0.3s ease, color 0.3s ease, border-color 0.3s ease;

            /* Dark mode styles (default) */
            background: rgba(0, 0, 0, 0.7);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        /* Light mode styles for contact form */
        body.light-mode .contact-form-container {
            background: rgba(240, 240, 240, 0.9);
            color: #181818;
            border: 1px solid rgba(24, 24, 24, 0.1);
        }

        /* Contact Form Close Button */
        .contact-close-button {
            position: absolute;
            top: 1rem; /* p-4 */
            right: 1rem; /* p-4 */
            background: none;
            border: none;
            cursor: pointer;
            font-size: 1.5rem; /* ri-xl */
            transition: color 0.3s ease, transform 0.3s ease;

            /* Dark mode styles (default) */
            color: rgba(255, 255, 255, 0.7);
        }

        body.light-mode .contact-close-button {
            color: #000000;
        }

        .contact-close-button:hover {
            transform: rotate(90deg);
        }

        /* Dark mode hover */
        .contact-close-button:hover {
            color: white;
        }

        /* Light mode hover */
        body.light-mode .contact-close-button:hover {
            color: #000000;
        }

        /* Form Elements Styling (using Tailwind utilities where possible) */
        .contact-form-container input[type="text"],
        .contact-form-container input[type="email"],
        .contact-form-container textarea {
            padding: 0.75rem 1rem; /* py-3 px-4 */
            border-radius: 0.375rem; /* rounded-md */
            width: 100%;
            margin-bottom: 1rem; /* mb-4 */
            transition: border-color 0.3s ease, box-shadow 0.3s ease, background-color 0.3s ease, color 0.3s ease;

            /* Dark mode styles (default) */
            background-color: rgba(0, 0, 0, 0.3);
            border: 1px solid rgba(255, 255, 255, 0.1);
            color: white;
        }

        /* Light mode styles for form inputs */
        body.light-mode .contact-form-container input[type="text"],
        body.light-mode .contact-form-container input[type="email"],
        body.light-mode .contact-form-container textarea {
            background-color: rgba(255, 255, 255, 0.6);
            border: 1px solid rgba(24, 24, 24, 0.1);
            color: #181818;
        }

        .contact-form-container input[type="text"]:focus,
        .contact-form-container input[type="email"]:focus,
        .contact-form-container textarea:focus {
            outline: none;
            border-color: rgba(74, 144, 226, 0.8); /* secondary color */
            box-shadow: 0 0 0 3px rgba(74, 144, 226, 0.3); /* Ring effect */
        }

        /* Dark mode focus */
        .contact-form-container input[type="text"]:focus,
        .contact-form-container input[type="email"]:focus,
        .contact-form-container textarea:focus {
            background-color: rgba(0, 0, 0, 0.5);
        }

        /* Light mode focus */
        body.light-mode .contact-form-container input[type="text"]:focus,
        body.light-mode .contact-form-container input[type="email"]:focus,
        body.light-mode .contact-form-container textarea:focus {
            background-color: rgba(255, 255, 255, 0.9);
        }

        .contact-form-container textarea {
            min-height: 120px; /* Adjust as needed */
            resize: vertical;
        }

        .contact-form-container button[type="submit"] {
            background-color: #4A90E2; /* Primary color */
            color: white;
            padding: 0.75rem 1.5rem; /* py-3 px-6 */
            border-radius: 0.375rem; /* rounded-md */
            font-weight: 600; /* font-semibold */
            width: 100%;
            cursor: pointer;
            transition: background-color 0.3s ease, transform 0.3s ease, box-shadow 0.3s ease;
            border: none;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            position: relative;
            overflow: hidden;
            z-index: 1;
        }

        .contact-form-container button[type="submit"]:hover {
            background-color: #3a7ac2; /* Darker shade */
            transform: translateY(-2px);
            box-shadow: 0 6px 8px rgba(0, 0, 0, 0.15);
        }

        /* Special animation effects for the submit button */
        .contact-form-container button[type="submit"]::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            transform: translate(-50%, -50%);
            z-index: -1;
            transition: width 0.6s ease-out, height 0.6s ease-out;
        }

        /* Button animation classes */
        .btn-pulse {
            animation: button-pulse 0.8s ease-out;
        }

        .btn-success {
            background-color: #4CD964 !important;
            box-shadow: 0 0 15px rgba(76, 217, 100, 0.5) !important;
        }

        .btn-success::before {
            background: rgba(76, 217, 100, 0.3);
        }

        .btn-error {
            background-color: #FF3B30 !important;
            box-shadow: 0 0 15px rgba(255, 59, 48, 0.5) !important;
        }

        .btn-error::before {
            background: rgba(255, 59, 48, 0.3);
        }

        /* Particle effects for the button */
        .btn-particle {
            position: absolute;
            background: white;
            border-radius: 50%;
            pointer-events: none;
            opacity: 0;
            z-index: 2;
        }

        /* Keyframes for button animations */
        @keyframes button-pulse {
            0% {
                box-shadow: 0 0 0 0 rgba(74, 144, 226, 0.7);
            }
            70% {
                box-shadow: 0 0 0 15px rgba(74, 144, 226, 0);
            }
            100% {
                box-shadow: 0 0 0 0 rgba(74, 144, 226, 0);
            }
        }

        @keyframes particle-fade {
            0% {
                transform: translate(0, 0);
                opacity: 1;
            }
            100% {
                transform: translate(var(--tx), var(--ty));
                opacity: 0;
            }
        }

        @keyframes button-success {
            0% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.1);
            }
            100% {
                transform: scale(1);
            }
        }

        @keyframes button-error {
            0%, 100% {
                transform: translateX(0);
            }
            10%, 30%, 50%, 70%, 90% {
                transform: translateX(-5px);
            }
            20%, 40%, 60%, 80% {
                transform: translateX(5px);
            }
        }

        /* Placeholder text color - Dark mode (default) */
        .contact-form-container ::placeholder {
            color: rgba(255, 255, 255, 0.5);
            opacity: 1; /* Firefox */
        }

        .contact-form-container :-ms-input-placeholder { /* Internet Explorer 10-11 */
            color: rgba(255, 255, 255, 0.5);
            opacity: 1;
        }

        .contact-form-container ::-ms-input-placeholder { /* Microsoft Edge */
            color: rgba(255, 255, 255, 0.5);
            opacity: 1;
        }

        /* Placeholder text color - Light mode */
        body.light-mode .contact-form-container ::placeholder {
            color: rgba(0, 0, 0, 0.7);
            opacity: 1; /* Firefox */
        }

        body.light-mode .contact-form-container :-ms-input-placeholder { /* Internet Explorer 10-11 */
            color: rgba(0, 0, 0, 0.7);
            opacity: 1;
        }

        body.light-mode .contact-form-container ::-ms-input-placeholder { /* Microsoft Edge */
            color: rgba(0, 0, 0, 0.7);
            opacity: 1;
        }

         /* Form result message */
         #form-result {
            margin-top: 1rem; /* mt-4 */
            min-height: 1.5rem; /* Ensure space is reserved */
            text-align: center;
         }



        /* Map Section */
        .map-section {
            position: relative;
            height: 300px;
            overflow: hidden;
        }
        .map-container {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
        }
        .map-container iframe {
            width: 100%;
            height: 100%;
            border: none;
        }

        /* Map Section Styling */
        .map-section h2 {
            color: var(--text-color);
            transition: color 0.3s ease;
        }

        /* Technology Icon Tooltips */
        .tech-icon-container {
            position: relative;
            display: inline-flex;
            justify-content: center;
            align-items: center;
        }
        .tech-tooltip {
            position: absolute;
            bottom: -25px;
            left: 50%;
            transform: translateX(-50%);
            background-color: var(--widget-bg);
            color: var(--text-color);
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.75rem;
            white-space: nowrap;
            opacity: 0;
            visibility: hidden;
            transition: opacity 0.2s, visibility 0.2s, background-color 0.3s ease, color 0.3s ease;
            pointer-events: none;
            z-index: 10;
            backdrop-filter: blur(5px);
            border: 1px solid var(--card-border);
        }
        .tech-icon-container:hover .tech-tooltip {
            opacity: 1;
            visibility: visible;
        }

        /* Tech icon hover effect */
        .tech-icon {
            color: var(--text-color-muted);
            transition: color 0.3s ease, transform 0.3s ease, filter 0.3s ease;
        }
        .tech-icon:hover {
            color: var(--text-color);
            filter: drop-shadow(0 0 15px var(--hover-glow));
        }
        /* Ensure tech icons use black in light mode */
        body.light-mode .tech-icon {
            color: #000000;
        }
        /* Map Container - Simple Clean Style */
        .map-container {
          position: relative;
          overflow: hidden; /* Ensures content fits rounded corners */
          width: 100%;
          padding-top: 50%; /* Aspect Ratio (height/width * 100). Adjust 50% for desired height */
          border-radius: 0.5rem; /* rounded-lg */
          background-color: var(--card-bg); /* Dark background placeholder */
          box-shadow: 0 4px 12px var(--shadow-color); /* Subtle shadow */
          border: 1px solid var(--card-border); /* Subtle border */
          transition: background-color 0.3s ease, box-shadow 0.3s ease, border-color 0.3s ease;
        }

        /* Footer with Image and Copyright Text */
        .footer {
          display: flex;
          justify-content: center;
          align-items: center;
          padding: 2rem 0;
          margin-top: 2rem;
          margin-bottom: 2rem;
          position: relative; /* For positioning the copyright text */
          padding-bottom: 5rem; /* Space for copyright text */
        }

        /* Copyright text that appears on hover */
        .footer-copyright {
          position: absolute;
          bottom: 1rem; /* Position below the image */
          left: 50%;
          transform: translateX(-50%);
          background-color: var(--widget-bg);
          color: var(--text-color);
          padding: 0.5rem 1rem;
          border-radius: 0.5rem;
          opacity: 0;
          transition: opacity 0.3s ease;
          z-index: 10;
        }

        /* Ensure footer copyright text is black in light mode */
        body.light-mode .footer-copyright {
          color: #000000;
        }

        /* Show copyright on footer hover */
        .footer:hover .footer-copyright {
          opacity: 1;
        }

        .footer-image {
          max-width: 200px; /* Reduced size from 300px to 200px */
          width: 100%;
          height: auto; /* Maintain aspect ratio */
          object-fit: contain; /* Show full image */
          /* No border, shadow, transition, or other effects */
          /* Disable image context menu options but allow hover */
          -webkit-touch-callout: none; /* iOS Safari */
          -webkit-user-select: none; /* Safari */
          -khtml-user-select: none; /* Konqueror HTML */
          -moz-user-select: none; /* Firefox */
          -ms-user-select: none; /* Internet Explorer/Edge */
          user-select: none; /* Non-prefixed version, currently supported by Chrome and Opera */
          /* Allow pointer events for hover but prevent right-click menu */
          cursor: pointer; /* Show pointer cursor on hover */
          margin-bottom: 5rem; /* Add space below the image for the copyright text */
          position: relative; /* For positioning */
          z-index: 5; /* Ensure image is above background but below copyright */
        }

        /* Theme-specific image visibility */
        .light-mode-image {
          display: none; /* Hidden by default (dark mode is default) */
        }

        .dark-mode-image {
          display: block; /* Visible by default (dark mode is default) */
        }

        /* Show light mode image when light mode is active */
        body.light-mode .light-mode-image {
          display: block;
        }

        /* Hide dark mode image when light mode is active */
        body.light-mode .dark-mode-image {
          display: none;
        }

        /* Map Container Wrapper - Simple */
        .map-container-wrapper {
          margin-bottom: 1rem;
          position: relative;
        }
        .map-container iframe {
          position: absolute;
          top: 0;
          left: 0;
          bottom: 0;
          right: 0;
          width: 100%;
          height: 100%;
          border: none; /* Remove default iframe border */
          /* Enhanced monochrome filter for black/white/gray aesthetic */
          filter: grayscale(100%) brightness(0.8) contrast(1.2) invert(92%);
        }

    </style>
</head>

<body>

    <!-- Loading Screen - Always Dark Mode -->
    <div id="loading-screen" class="loading-screen">
        <div class="text-6xl mb-8 animate-bounce">
            <i class="ri-code-s-slash-line" style="color: #ffffff; font-size: 4rem; filter: drop-shadow(0 0 10px rgba(255, 255, 255, 0.5));"></i>
        </div>
        <div id="loading-bar-container">
           <div id="loading-bar"></div>
        </div>
        <div id="loading-percentage">0%</div>
        <div class="loading-footer">Built using <b><a href="https://www.nextstarter.xyz/">NextJS</a></b> and <b><a href="https://code.visualstudio.com/">VSCode</b></a></b></div>
    </div>

    <!-- Particle Background Container -->
    <div id="tsparticles"></div>

    <!-- Fixed UI Elements -->
    <div class="clock">
        <span id="time"></span>
        <span id="date"></span>
    </div>

    <!-- iOS-style Theme Toggle Switch -->
    <button class="theme-toggle" id="theme-toggle" title="Modes">
        <div class="switch-track">
            <div class="switch-handle"></div>
            <i class="ri-moon-clear-line icon-moon" title="Dark"></i>
            <i class="ri-flashlight-line icon-sun" title="Light"></i>
        </div>
    </button>

    <button class="weather-toggle">
        <i class="ri-cloud-line ri-lg"></i>
    </button>

    <!-- Add Contact Toggle Button -->
    <button class="contact-toggle" id="contact-toggle-button" title="">
        <i class="ri-at-line ri-lg"></i>
    </button>

    <!-- Spotify Toggle Button -->
    <button class="spotify-toggle" id="spotify-toggle-button" title="">
        <i class="ri-spotify-line ri-lg"></i>
    </button>

    <!-- Spotify Widget -->
    <div class="spotify-widget" id="spotify-widget">
        <div class="text-xl font-bold mb-3 flex items-center">
        </div>
        <iframe src="https://open.spotify.com/embed/playlist/6Tjt6XXbRBwSw5OTowp7NL?utm_source=generator"
               width="100%" height="380" frameborder="0" allowtransparency="true"
               allow="encrypted-media" loading="lazy">
        </iframe>
    </div>

    <div class="weather-widget">
        <div class="text-xl font-bold mb-2">Montreal</div>
        <div class="weather-info"></div>
        <div class="mt-4">
            <h3 class="text-sm font-semibold mb-2">Forecast</h3>
            <div class="forecast-container"></div>
        </div>
    </div>

    <!-- Main Content Area -->
    <div class="container mx-auto px-4 py-12 relative z-10">

        <!-- Header Section -->
        <header class="text-center mb-16">
            <div class="profile-border" id="profile-container">
                <video id="profile-video" autoplay muted playsinline disablepictureinpicture preload="auto">
                    <source src="uzy.mp4" type="video/mp4">
                    <!-- Fallback to image if video doesn't load -->
                    <img src="http://uzy.rf.gd/yusuf.jpg" alt="Profile">
                </video>
            </div>
            <h1 class="text-5xl font-bold mb-4">Yusuf</h1>
            <div class="flex items-center justify-center gap-2 text-base mb-8 theme-text-muted">
                <span>Freelance Web Developer</span>
                <span class="flex items-center justify-center">
                    <span class="w-2 h-2 bg-green-500 rounded-full animate-pulse"></span>
                </span>
            </div>
            <p class="text-lg mb-8 theme-text-muted">Learning, building and gaining skills with every click.</p>
            <div class="flex justify-center gap-6">
                <a href="javascript:void(0);" onclick="openContactForm();" class="hover:text-gray-300 transition-all duration-300 hover:scale-125"><i class="ri-mail-line ri-lg"></i></a>
                <a href="https://www.instagram.com/uzygram" target="_blank" rel="noopener noreferrer" class="hover:text-gray-300 transition-all duration-300 hover:scale-125"><i class="ri-instagram-line ri-lg"></i></a>
                <a href="https://github.com/uzygram" target="_blank" rel="noopener noreferrer" class="hover:text-gray-300 transition-all duration-300 hover:scale-125"><i class="ri-github-line ri-lg"></i></a>
                <a href="https://linktr.ee/uzymtl" target="_blank" rel="noopener noreferrer" class="hover:text-gray-300 transition-all duration-300 hover:scale-125"><i class="ri-tree-line ri-lg"></i></a>
                <a href="http://uzy.rf.gd/target.php" target="_blank" rel="noopener noreferrer" class="hover:text-gray-300 transition-all duration-300 hover:scale-125"><i class="ri-linkedin-line ri-lg"></i></a>
            </div>
            <div class="mt-16">
                <h3 class="text-lg theme-text-muted mb-4 text-center"><b>Experiences with</b></h3>
                <!-- Tooltip styles moved to head section -->

                <div class="flex justify-center gap-6 flex-wrap">
                    <div class="tech-icon-container">
                        <i class="ri-html5-line ri-2x transition-all duration-300 hover:scale-125 tech-icon"></i>
                        <span class="tech-tooltip">HTML5</span>
                    </div>
                    <div class="tech-icon-container">
                        <i class="ri-css3-line ri-2x transition-all duration-300 hover:scale-125 tech-icon"></i>
                        <span class="tech-tooltip">CSS3</span>
                    </div>
                    <div class="tech-icon-container">
                        <i class="ri-tailwind-css-line ri-2x transition-all duration-300 hover:scale-125 tech-icon"></i>
                        <span class="tech-tooltip">Tailwind CSS</span>
                    </div>
                    <div class="tech-icon-container">
                        <i class="ri-javascript-line ri-2x transition-all duration-300 hover:scale-125 tech-icon"></i>
                        <span class="tech-tooltip">JavaScript</span>
                    </div>
                    <div class="tech-icon-container">
                        <i class="ri-reactjs-line ri-2x transition-all duration-300 hover:scale-125 tech-icon"></i>
                        <span class="tech-tooltip">React</span>
                    </div>
                    <div class="tech-icon-container">
                        <i class="ri-nextjs-line ri-2x transition-all duration-300 hover:scale-125 tech-icon"></i>
                        <span class="tech-tooltip">Next.js</span>
                    </div>
                    <div class="tech-icon-container">
                        <i class="ri-vuejs-line ri-2x transition-all duration-300 hover:scale-125 tech-icon"></i>
                        <span class="tech-tooltip">Vue.js</span>
                    </div>
                    <div class="tech-icon-container">
                        <i class="ri-nodejs-line ri-2x transition-all duration-300 hover:scale-125 tech-icon"></i>
                        <span class="tech-tooltip">Node.js</span>
                    </div>
                </div>
            </div>
        </header>

        <!-- Projects Section -->
        <section class="mb-16">
            <h2 class="text-2xl font-bold mb-8">+ MY SERVICES</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div class="project-card">
                    <div class="border-effect bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500"></div>
                    <img src="http://uzy.rf.gd/ysf.jpg" alt="Project 1" class="w-full h-48 object-cover">
                    <h3 class="text-xl font-bold mb-2">WEB DEVELOPMENT 🪐</h3>
                    <p class="theme-text-muted">Specialized in creating responsive websites with modern technologies. From static front-end pages to full-stack apps — Helping brands grow through effective web solutions.</p>
                </div>
                <div class="project-card">
                    <div class="border-effect bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500"></div>
                    <img src="http://uzy.rf.gd/bg.jpg" alt="Project 2" class="w-full h-48 object-cover">
                    <h3 class="text-xl font-bold mb-2">UI/UX ✨</h3>
                    <p class="theme-text-muted">Addicted to clean UI, blending aesthetics and bringing futuristic interfaces to reality — Perfectly aligned with your identity.</p>
                </div>
            </div>
        </section>

        <!-- Services Section -->
        <section class="mb-16 mb-24">
            <h2 class="text-2xl font-bold mb-8">+ MY TOOLS</h2>
            <div class="space-y-6">
                 <div class="service-card">
                    <div class="service-card-border bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500"></div>
                    <div class="flex items-center gap-4 mb-2">
                        <i class="ri-codepen-line ri-lg"></i>
                        <h3 class="text-xl font-bold">VISUAL STUDIO CODE</h3>
                    </div>
                    <p class="theme-text-muted">VSCode is my primary environment, providing unmatched flexibility that optimizes my coding experience for maximum productivity and comfort.</p>
                </div>
                 <div class="service-card">
                     <div class="service-card-border bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500"></div>
                    <div class="flex items-center gap-4 mb-2">
                        <i class="ri-vercel-line ri-lg"></i>
                        <h3 class="text-xl font-bold">VERCEL</h3>
                    </div>
                    <p class="theme-text-muted">My preferred platform for fast, reliable deployments and modern frontend frameworks.</p>
                </div>
                 <div class="service-card">
                     <div class="service-card-border bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500"></div>
                    <div class="flex items-center gap-4 mb-2">
                        <i class="ri-copilot-fill ri-lg"></i>
                        <h3 class="text-xl font-bold">GITHUB</h3>
                    </div>
                    <p class="theme-text-muted">GitHub serves as the backbone of my development process. With its community and collaborative capabilities, it's an indispensable tool for my ecosystem.</p>
                </div>
            </div>
        </section>

        <!-- Map Section -->
        <section class="mb-16 map-section">
             <h2 class="text-2xl font-bold mb-8">+ MY LOCATION</h2>
             <div class="map-container-wrapper">
                 <div class="map-container">
                     <iframe
                        src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d89466.98000211321!2d-73.71074746267145!3d45.51323649371029!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x4cc91a4d31166b3d%3A0xe16252d7fe06209e!2sVille-Marie%2C%20Montreal%2C%20QC!5e0!3m2!1sen!2sca!4v1745422664417!5m2!1sen!2sca"
                        allowfullscreen=""
                        loading="lazy"
                        referrerpolicy="no-referrer-when-downgrade"
                        title="Map of Montreal">
                     </iframe>
                 </div>
             </div>
        </section>

        <!-- Footer with Image and Copyright Text -->
        <footer class="footer">
            <img src="yk.png" alt="Yusuf K." class="footer-image dark-mode-image" draggable="false" oncontextmenu="return false;">
            <img src="yk2.png" alt="Yusuf K." class="footer-image light-mode-image" draggable="false" oncontextmenu="return false;">
            <div class="footer-copyright">© 2025 Yusuf K.</div>
        </footer>

    </div>

    <!-- Contact Form Overlay -->
    <div class="contact-overlay" id="contact-overlay">
        <div class="contact-form-container">
            <button class="contact-close-button" id="contact-close-button" title="Close">
                <i class="ri-close-line"></i>
            </button>
            <h2 class="text-2xl font-bold mb-2 text-center">Inquiries</h2>
            <p class="text-base theme-text-muted mb-6 text-center">
                Have an exciting project in mind or need assistance with? Feel free to shoot me a message — I’d be happy to connect.
            </p>
            <form id="contact-form" action="https://formcarry.com/s/S05h5ApUuLu" method="POST" accept-charset="UTF-8">
                <!-- Formcarry configuration -->
                <input type="hidden" name="_subject" value="Form Submission Yusuf.dev">
                <!-- Honeypot field for spam prevention -->
                <input type="hidden" name="_gotcha" value="">
                <!-- Add CORS headers -->
                <input type="hidden" name="_cors" value="true">

                <div class="mb-4">
                    <input type="text" name="name" placeholder="Your Name" required>
                </div>
                <div class="mb-4">
                    <input type="email" name="email" placeholder="Your Email" required>
                </div>
                <div class="mb-4">
                    <input type="text" name="project" placeholder="Your Project" required>
                </div>
                <div class="mb-4">
                    <textarea name="message" placeholder="Your Message" required></textarea>
                </div>
                <!-- Formspree doesn't need these fields, but we'll keep a timestamp for our own rate limiting -->
                <input type="hidden" name="timestamp" id="form-timestamp" value="">

                <div class="text-center">
                    <button type="submit" id="contact-submit-button">Send Message</button>
                </div>
                <div id="form-result" class="mt-4 text-sm"></div> <!-- For feedback -->
                <div class="mt-4 text-sm text-center theme-text-muted">
                    If you experience any issues with the form, you can also email me directly at
                    <a href="mailto:<EMAIL>" class="underline hover:text-blue-400"><EMAIL></a>
                </div>
            </form>
        </div>
    </div>



    <!-- JavaScript -->
    <script>
        // Formcarry doesn't require initialization
        // This function is kept as a placeholder in case we need to add initialization code later
        async function initializeFormcarry() {
            try {
                console.log('Formcarry form initialized');
                return true;
            } catch (error) {
                console.error('Formcarry initialization error:', error);
                return false;
            }
        }

        // Initialize Formcarry when page loads
        initializeFormcarry();

        // Global function to open contact form - accessible from anywhere
        function openContactForm() {
            const contactOverlay = document.getElementById('contact-overlay');
            if (contactOverlay) {
                contactOverlay.classList.add('active');
                document.body.style.overflow = 'hidden'; // Prevent background scrolling
            }
        }

        document.addEventListener('DOMContentLoaded', () => {
            // --- Loading Animation ---
            const loadingScreen = document.getElementById('loading-screen');
            const loadingBar = document.getElementById('loading-bar');
            const loadingPercentage = document.getElementById('loading-percentage');
            let progress = 0;
            const loadingInterval = setInterval(() => {
                progress += 1;
                // Ensure progress doesn't exceed 100 visually if interval runs fast
                const displayProgress = Math.min(progress, 100);
                loadingBar.style.width = `${displayProgress}%`;
                loadingPercentage.textContent = `${displayProgress}%`;

                if (progress >= 100) {
                    clearInterval(loadingInterval);
                    setTimeout(() => {
                        loadingScreen.style.opacity = '0';
                        // Use transitionend event for more reliable removal
                        loadingScreen.addEventListener('transitionend', () => {
                           if (loadingScreen.style.opacity === '0') { // Check final state
                             loadingScreen.style.display = 'none';

                             // Initialize profile video after loading screen is dismissed
                             const profileVideo = document.getElementById('profile-video');
                             if (profileVideo) {
                                 profileVideo.play().catch(e => console.log('Video play failed after loading:', e));
                             }
                           }
                        }, { once: true }); // Remove listener after it runs once
                    }, 300); // Wait a bit after 100%
                }
            }, 30); // Loading speed

            // --- Theme Toggle ---
            const themeToggle = document.getElementById('theme-toggle');

            // Force dark mode as default, regardless of saved preference
            // Remove light-mode class if it exists
            document.body.classList.remove('light-mode');
            // Save dark mode preference to localStorage
            localStorage.setItem('theme', 'dark');

            // Function to update particles based on theme
            function updateParticles() {
                const isDarkMode = !document.body.classList.contains('light-mode');
                const particleColor = isDarkMode ? "#ffffff" : "#000000";
                const bgColor = isDarkMode ? "#000000" : "#ffffff";

                tsParticles.load("tsparticles", {
                    fpsLimit: 60,
                    particles: {
                        number: {
                            value: isDarkMode ? 80 : 100, // More particles in light mode for better visibility
                            density: {
                                enable: true,
                                value_area: 800
                            }
                        },
                        color: {
                            value: particleColor // Particle color based on theme
                        },
                        shape: {
                            type: "circle"
                        },
                        opacity: {
                            value: isDarkMode ? 0.5 : 0.8, // Higher opacity for light mode
                            random: true,
                            anim: {
                                enable: true,
                                speed: 0.5,
                                opacity_min: isDarkMode ? 0.1 : 0.3, // Higher minimum opacity for light mode
                                sync: false
                            }
                        },
                        size: {
                            value: isDarkMode ? 2 : 2.5, // Slightly larger particles for light mode
                            random: true,
                            anim: {
                                enable: false
                            }
                        },
                        line_linked: {
                            enable: false // Disable lines connecting particles
                        },
                        move: {
                            enable: true,
                            speed: 0.5, // Particle speed
                            direction: "none",
                            random: true,
                            straight: false,
                            out_mode: "out",
                            bounce: false,
                            attract: {
                                enable: false
                            }
                        }
                    },
                    interactivity: {
                        detect_on: "canvas",
                        events: {
                            onhover: {
                                enable: false, // Disable hover effects
                            },
                            onclick: {
                                enable: false, // Disable click effects
                            },
                            resize: true
                        }
                    },
                    detectRetina: true,
                    background: {
                       color: bgColor, // Background color based on theme
                    }
                });
            }

            // Initialize particles based on current theme
            updateParticles();

            // Prevent context menu on footer images but allow hover
            const footerImages = document.querySelectorAll('.footer-image');
            const footer = document.querySelector('.footer');

            // Prevent context menu on images
            footerImages.forEach(image => {
                // Prevent right-click context menu
                image.addEventListener('contextmenu', (e) => {
                    e.preventDefault();
                    return false;
                });

                // Remove pointer-events: none to allow hover
                image.style.pointerEvents = 'auto';
            });

            // Toggle theme with iOS-style switch animation
            if (themeToggle) {
                themeToggle.addEventListener('click', () => {
                    // Prevent multiple clicks during animation
                    if (document.body.classList.contains('theme-transitioning')) {
                        return;
                    }

                    // Add transitioning class for animation
                    document.body.classList.add('theme-transitioning');

                    // Toggle light mode immediately for a more responsive feel
                    document.body.classList.toggle('light-mode');

                    // Save theme preference
                    const isLightMode = document.body.classList.contains('light-mode');
                    localStorage.setItem('theme', isLightMode ? 'light' : 'dark');

                    // Update particles
                    updateParticles();

                    // Update Skycons color based on theme
                    skycons.color = document.body.classList.contains('light-mode') ? "black" : "white";

                    // Remove transitioning class after animation completes
                    setTimeout(() => {
                        document.body.classList.remove('theme-transitioning');
                    }, 600); // Shorter duration for a snappier feel
                });
            }


            // --- Profile Video Setup ---
            const profileVideo = document.getElementById('profile-video');
            if (profileVideo) {
                let videoRestartTimeout; // Variable to store the timeout ID

                // Function to restart the video
                function restartProfileVideo() {
                    // Reset the video to the beginning
                    profileVideo.currentTime = 0;
                    // Ensure the video is playing
                    profileVideo.play().catch(e => console.log('Video play failed:', e));
                }

                // Listen for the video ending
                profileVideo.addEventListener('ended', () => {
                    console.log('Video ended, will restart in 5 seconds');
                    // Clear any existing timeout
                    if (videoRestartTimeout) {
                        clearTimeout(videoRestartTimeout);
                    }
                    // Set a timeout to restart the video after 5 seconds
                    videoRestartTimeout = setTimeout(() => {
                        restartProfileVideo();
                    }, 5000); // 5 seconds delay
                });

                // Handle video loading errors
                profileVideo.addEventListener('error', () => {
                    console.log('Video loading error, falling back to image');
                    // Clear any restart timeout if video fails
                    if (videoRestartTimeout) {
                        clearTimeout(videoRestartTimeout);
                    }
                });

                // Ensure video plays when page becomes visible
                document.addEventListener('visibilitychange', () => {
                    if (!document.hidden) {
                        if (profileVideo.paused) {
                            profileVideo.play().catch(e => console.log('Video play failed on visibility change:', e));
                        }
                    } else {
                        // If page is hidden, clear any pending restart timeout
                        if (videoRestartTimeout) {
                            clearTimeout(videoRestartTimeout);
                        }
                    }
                });

                // Force play on load
                window.addEventListener('load', () => {
                    profileVideo.play().catch(e => console.log('Video play failed on load:', e));
                });

                // Restart video after loading screen is dismissed
                const loadingScreen = document.getElementById('loading-screen');
                if (loadingScreen) {
                    loadingScreen.addEventListener('transitionend', () => {
                        if (loadingScreen.style.opacity === '0') {
                            setTimeout(() => {
                                profileVideo.play().catch(e => console.log('Video play failed after loading:', e));
                            }, 100);
                        }
                    });
                }
            }

            // --- Profile Orbs Removed ---


            // --- Clock and Date ---
            function updateClock() {
                const now = new Date();
                const timeElement = document.getElementById('time');
                const dateElement = document.getElementById('date');

                if (timeElement && dateElement) { // Check if elements exist
                    // Format time: HH:MM:SS (24-hour)
                    const time = now.toLocaleTimeString('en-GB'); // 'en-GB' often gives HH:MM:SS

                    // Format date: ShortWeekday, ShortMonth Day
                    const date = now.toLocaleDateString('en-US', {
                        weekday: 'short',
                        month: 'short',
                        day: 'numeric'
                    });

                    timeElement.textContent = time;
                    dateElement.textContent = date;
                }
            }
            setInterval(updateClock, 1000);
            updateClock(); // Initial call

            // --- Weather Widget ---
            const weatherToggle = document.querySelector('.weather-toggle');
            const weatherWidget = document.querySelector('.weather-widget');
            let weatherVisible = false;
            let weatherCloseTimer = null; // Variable to hold the timeout ID

            // Function to handle closing the widget (to avoid repetition)
            function closeWeatherWidget() {
                if (!weatherVisible) return; // Already closed or closing

                weatherVisible = false;
                if (weatherCloseTimer) {
                    clearTimeout(weatherCloseTimer); // Clear auto-close timer if manually closed
                    weatherCloseTimer = null;
                }
                weatherWidget.classList.remove('active');
                // Listen for transition end to hide the element fully
                weatherWidget.addEventListener('transitionend', () => {
                    if (!weatherWidget.classList.contains('active')) {
                        weatherWidget.style.display = 'none';
                    }
                }, { once: true });
            }

            // --- Spotify Widget ---
            const spotifyToggle = document.getElementById('spotify-toggle-button');
            const spotifyWidget = document.getElementById('spotify-widget');
            let spotifyVisible = false;
            let spotifyCloseTimer = null;

            function closeSpotifyWidget() {
                if (!spotifyVisible) return; // Already closed or closing

                spotifyVisible = false;
                if (spotifyCloseTimer) {
                    clearTimeout(spotifyCloseTimer);
                    spotifyCloseTimer = null;
                }
                spotifyWidget.classList.remove('active');
                // Listen for transition end to hide the element fully
                spotifyWidget.addEventListener('transitionend', () => {
                    if (!spotifyWidget.classList.contains('active')) {
                        // Keep the widget in the DOM but hidden visually
                        // This allows the music to continue playing
                        spotifyWidget.style.visibility = 'hidden';
                        // Don't set display: none to keep the iframe active
                    }
                }, { once: true });
            }

            if (weatherToggle && weatherWidget) { // Check if elements exist
                weatherToggle.addEventListener('click', () => {
                    // Clear any existing timer when toggle is clicked
                    if (weatherCloseTimer) {
                        clearTimeout(weatherCloseTimer);
                        weatherCloseTimer = null;
                    }

                    if (!weatherVisible) { // If opening the widget
                        // Close spotify widget if open
                        if (spotifyVisible) {
                            closeSpotifyWidget();
                        }

                        weatherVisible = true;
                        weatherWidget.style.display = 'block';
                        // Timeout needed to allow display:block before transition starts
                        setTimeout(() => {
                            weatherWidget.classList.add('active');
                        }, 10);
                        fetchWeather(); // Fetch weather when opening

                        // Start the auto-close timer
                        weatherCloseTimer = setTimeout(() => {
                            console.log("Auto-closing weather widget after 4 seconds.");
                            closeWeatherWidget(); // Call the close function
                        }, 4000); // 4000 milliseconds = 4 seconds

                    } else { // If closing the widget manually
                       closeWeatherWidget();
                    }
                });
            }

            if (spotifyToggle && spotifyWidget) {
                spotifyToggle.addEventListener('click', () => {
                    // Clear any existing timer when toggle is clicked
                    if (spotifyCloseTimer) {
                        clearTimeout(spotifyCloseTimer);
                        spotifyCloseTimer = null;
                    }

                    if (!spotifyVisible) { // If opening the widget
                        // Close weather widget if open
                        if (weatherVisible) {
                            closeWeatherWidget();
                        }

                        spotifyVisible = true;
                        // If the widget was just hidden but not removed from DOM (for continuous playback)
                        if (spotifyWidget.style.visibility === 'hidden') {
                            spotifyWidget.style.visibility = 'visible';
                        } else {
                            spotifyWidget.style.display = 'block';
                        }
                        // Timeout needed to allow display:block before transition starts
                        setTimeout(() => {
                            spotifyWidget.classList.add('active');
                        }, 10);

                        // No auto-close for Spotify - let user enjoy the music
                    } else { // If closing the widget manually
                        closeSpotifyWidget();
                    }
                });

                // Close on ESC key (same as contact form)
                document.addEventListener('keydown', (event) => {
                    if (event.key === 'Escape' && spotifyVisible) {
                        closeSpotifyWidget();
                    }
                });

                // Close when clicking anywhere on the document
                document.addEventListener('click', (event) => {
                    // Only close if widget is visible and click is outside the widget and toggle button
                    if (spotifyVisible &&
                        !spotifyWidget.contains(event.target) &&
                        event.target !== spotifyToggle &&
                        !spotifyToggle.contains(event.target)) {
                        closeSpotifyWidget();
                    }
                });
            }

            // Instantiate Skycons with theme-aware color
            const skycons = new Skycons({
                "color": document.body.classList.contains('light-mode') ? "black" : "white"
            });

            async function fetchWeather() {
                const weatherInfo = document.querySelector('.weather-info');
                const forecastContainer = document.querySelector('.forecast-container');
                if (!weatherInfo || !forecastContainer) return;

                const apiKey = 'fb7daab92cf971a98f67b33cf44d3504'; // Replace YOUR_API_KEY
                const city = 'Montreal';
                const units = 'metric';
                const weatherUrl = `https://api.openweathermap.org/data/2.5/weather?q=${city}&units=${units}&appid=${apiKey}`;
                const forecastUrl = `https://api.openweathermap.org/data/2.5/forecast?q=${city}&units=${units}&appid=${apiKey}`;

                // --- Remove Loading Indicator ---
                // Set both areas to blank initially
                weatherInfo.innerHTML = '';
                forecastContainer.innerHTML = '';
                // --- End Loading Indicator Removal ---

                // --- IMPORTANT: Remove previously added Skycons ---
                // Find all canvas elements added by skycons (if any existed) and remove them
                // We know the main ID and the pattern for forecast IDs
                skycons.remove("weather-icon-canvas");
                for (let i = 0; i < 5; i++) { // Check a few potential previous forecast icons
                    skycons.remove(`forecast-icon-${i}`);
                }
                // --- End Removal ---

                try {
                    const [weatherResponse, forecastResponse] = await Promise.all([
                        fetch(weatherUrl),
                        fetch(forecastUrl)
                    ]);

                    if (!weatherResponse.ok) throw new Error(`Weather fetch failed: ${weatherResponse.statusText}`);
                    if (!forecastResponse.ok) throw new Error(`Forecast fetch failed: ${forecastResponse.statusText}`);

                    const weatherData = await weatherResponse.json();
                    const forecastData = await forecastResponse.json();

                    const skyconIdentifier = getSkyconIdentifier(
                        weatherData.weather[0].id,
                        weatherData.dt,
                        weatherData.sys.sunrise,
                        weatherData.sys.sunset
                    );

                    // Update current weather HTML with canvas and increased text sizes
                    weatherInfo.innerHTML = `
                      <div class="flex items-start gap-3 mb-2">
                        <canvas id="weather-icon-canvas" width="48" height="48"></canvas>
                        <div>
                          <div class="flex items-center gap-2 mb-1">
                            <i class="ri-temp-cold-line ri-lg"></i>
                            <span class="text-2xl">${Math.round(weatherData.main.temp)}°C</span>
                          </div>
                          <div class="capitalize text-base">${weatherData.weather[0].description}</div>
                        </div>
                      </div>
                      <div class="flex items-center gap-2 text-base mb-1">
                        <i class="ri-drop-line"></i> <span>${weatherData.main.humidity}% humidity</span>
                      </div>
                      <div class="flex items-center gap-2 text-base">
                        <i class="ri-windy-line"></i> <span>${Math.round(weatherData.wind.speed * 3.6)} km/h</span>
                      </div>
                    `;
                    // Add the main icon to the Skycons instance
                    skycons.add("weather-icon-canvas", skyconIdentifier);

                    // --- Forecast Update ---
                    const dailyForecasts = {};
                    forecastData.list.forEach(item => {
                        const date = new Date(item.dt * 1000);
                        const dayKey = date.toISOString().split('T')[0]; // YYYY-MM-DD
                        if (!dailyForecasts[dayKey] || Math.abs(date.getHours() - 12) < Math.abs(new Date(dailyForecasts[dayKey].dt * 1000).getHours() - 12)) {
                            if (date.getDate() !== new Date().getDate()) {
                                dailyForecasts[dayKey] = item;
                            }
                        }
                    });
                    const nextThreeDays = Object.values(dailyForecasts).slice(0, 3);

                    // Clear previous forecast container content (already done above)
                    // forecastContainer.innerHTML = ''; // No need to clear again

                    nextThreeDays.forEach((forecast, index) => {
                      const date = new Date(forecast.dt * 1000);
                      const dayName = date.toLocaleDateString('en-US', { weekday: 'short' });
                      const forecastSkyconId = getSkyconIdentifier(forecast.weather[0].id, forecast.dt, weatherData.sys.sunrise, weatherData.sys.sunset);
                      const forecastCanvasId = `forecast-icon-${index}`;

                      const forecastItem = document.createElement('div');
                      forecastItem.className = 'forecast-item items-center';
                      // Update forecast HTML with increased text sizes
                      forecastItem.innerHTML = `
                        <div class="w-1/4 text-left text-base"><span>${dayName}</span></div>
                        <div class="w-1/4 flex justify-center"><canvas id="${forecastCanvasId}" width="24" height="24"></canvas></div>
                        <div class="w-1/4 text-center capitalize text-sm"><span>${forecast.weather[0].description}</span></div>
                        <div class="w-1/4 text-right text-base">
                          <span>${Math.round(forecast.main.temp_max)}°</span> / <span class="text-gray-400">${Math.round(forecast.main.temp_min)}°</span>
                        </div>
                      `;
                      forecastContainer.appendChild(forecastItem);
                      // Add forecast icon to the Skycons instance AFTER appending the element
                      skycons.add(forecastCanvasId, forecastSkyconId);
                    });

                    // --- Play animations AFTER all icons have been added ---
                    skycons.play();

                } catch (error) {
                    console.error('Error fetching weather:', error);
                    // Display error in the main info area if fetch fails
                    weatherInfo.innerHTML = `<div class="text-red-400"><i class="ri-error-warning-line mr-2"></i> Failed to load weather data</div>`;
                    // Clear any potentially added icons on error
                    skycons.remove("weather-icon-canvas");
                     for (let i = 0; i < 3; i++) {
                        skycons.remove(`forecast-icon-${i}`);
                    }
                }
            }

            // Helper function to get Skycon identifier based on weather condition code and time
            function getSkyconIdentifier(code, currentTimeUnix, sunriseUnix, sunsetUnix) {
                const isDay = currentTimeUnix > sunriseUnix && currentTimeUnix < sunsetUnix;

                if (code >= 200 && code < 300) return Skycons.RAIN; // Thunderstorm -> RAIN (or SLEET maybe)
                if (code >= 300 && code < 400) return Skycons.RAIN; // Drizzle -> RAIN
                if (code >= 500 && code < 600) return Skycons.RAIN; // Rain -> RAIN
                if (code >= 600 && code < 700) return Skycons.SNOW; // Snow -> SNOW
                if (code >= 700 && code < 800) return Skycons.FOG;  // Atmosphere -> FOG
                if (code === 800) return isDay ? Skycons.CLEAR_DAY : Skycons.CLEAR_NIGHT; // Clear
                if (code === 801) return isDay ? Skycons.PARTLY_CLOUDY_DAY : Skycons.PARTLY_CLOUDY_NIGHT; // Few clouds
                if (code > 801) return Skycons.CLOUDY; // Scattered, broken, overcast clouds -> CLOUDY

                // Default fallback
                return isDay ? Skycons.CLEAR_DAY : Skycons.CLEAR_NIGHT;
            }

            // --- Contact Form Logic ---
            const contactToggle = document.getElementById('contact-toggle-button');
            const contactOverlay = document.getElementById('contact-overlay');
            const contactClose = document.getElementById('contact-close-button');
            const contactForm = document.getElementById('contact-form');
            const formResult = document.getElementById('form-result');
            const submitButton = document.getElementById('contact-submit-button');

            function closeContactForm() {
                // Add closing animation
                if (submitButton.classList.contains('btn-success')) {
                    // If form was successfully submitted, add a final particle burst
                    for (let i = 0; i < 10; i++) {
                        setTimeout(() => {
                            createParticle(submitButton);
                        }, i * 30);
                    }
                }

                contactOverlay.classList.remove('active');
                document.body.style.overflow = ''; // Restore background scrolling

                // Clear result message and reset form after animation
                setTimeout(() => {
                    formResult.textContent = '';
                    formResult.className = 'mt-4 text-sm'; // Reset class
                    contactForm.reset();
                    submitButton.disabled = false;
                    submitButton.textContent = 'Send Message';
                    submitButton.classList.remove('btn-success', 'btn-error', 'btn-pulse');
                    submitButton.style.animation = '';
                }, 400); // Match CSS transition duration
            }

            if (contactToggle && contactOverlay && contactClose && contactForm) {
                contactToggle.addEventListener('click', openContactForm);
                contactClose.addEventListener('click', closeContactForm);

                // Close on clicking background overlay
                contactOverlay.addEventListener('click', (event) => {
                    if (event.target === contactOverlay) { // Ensure click is on overlay itself
                        closeContactForm();
                    }
                });

                // Close on ESC key
                document.addEventListener('keydown', (event) => {
                    if (event.key === 'Escape' && contactOverlay.classList.contains('active')) {
                        closeContactForm();
                    }
                });

                // Create particles function for button click effect
                function createButtonParticles(button) {
                    // Create a ripple effect
                    const ripple = document.createElement('span');
                    ripple.style.position = 'absolute';
                    ripple.style.top = '50%';
                    ripple.style.left = '50%';
                    ripple.style.transform = 'translate(-50%, -50%)';
                    ripple.style.width = '0';
                    ripple.style.height = '0';
                    ripple.style.backgroundColor = 'rgba(255, 255, 255, 0.3)';
                    ripple.style.borderRadius = '50%';
                    ripple.style.transition = 'all 0.6s ease-out';
                    button.appendChild(ripple);

                    // Expand the ripple
                    setTimeout(() => {
                        const buttonWidth = button.offsetWidth;
                        const buttonHeight = button.offsetHeight;
                        const diameter = Math.max(buttonWidth, buttonHeight) * 2;
                        ripple.style.width = `${diameter}px`;
                        ripple.style.height = `${diameter}px`;
                        ripple.style.opacity = '0';
                    }, 10);

                    // Remove the ripple after animation completes
                    setTimeout(() => {
                        if (ripple.parentNode) {
                            ripple.parentNode.removeChild(ripple);
                        }
                    }, 700);

                    // Create flying particles
                    const particleCount = 15;
                    for (let i = 0; i < particleCount; i++) {
                        createParticle(button);
                    }
                }

                // Create a single particle
                function createParticle(button) {
                    const particle = document.createElement('span');
                    particle.className = 'btn-particle';

                    // Random size between 5-10px
                    const size = Math.random() * 5 + 5;
                    particle.style.width = `${size}px`;
                    particle.style.height = `${size}px`;

                    // Random position within the button
                    const buttonRect = button.getBoundingClientRect();
                    const offsetX = Math.random() * buttonRect.width;
                    const offsetY = Math.random() * buttonRect.height;
                    particle.style.left = `${offsetX}px`;
                    particle.style.top = `${offsetY}px`;

                    // Random direction and distance
                    const angle = Math.random() * Math.PI * 2;
                    const distance = Math.random() * 100 + 50;
                    const tx = Math.cos(angle) * distance;
                    const ty = Math.sin(angle) * distance;

                    // Set CSS variables for the animation
                    particle.style.setProperty('--tx', `${tx}px`);
                    particle.style.setProperty('--ty', `${ty}px`);

                    // Add to button
                    button.appendChild(particle);

                    // Animate and remove
                    requestAnimationFrame(() => {
                        particle.style.animation = `particle-fade 1s ease-out forwards`;
                    });

                    // Remove particle after animation
                    setTimeout(() => {
                        if (particle.parentNode) {
                            particle.parentNode.removeChild(particle);
                        }
                    }, 1000);
                }

                // Handle Form Submission with Fetch API
                contactForm.addEventListener('submit', async function (e) {
                    e.preventDefault(); // Prevent default browser submission

                    // Trigger the click animation
                    createButtonParticles(submitButton);
                    submitButton.classList.add('btn-pulse');

                    // Remove pulse animation after it completes
                    setTimeout(() => {
                        submitButton.classList.remove('btn-pulse');
                    }, 800);

                    submitButton.disabled = true;
                    submitButton.textContent = 'Sending...';
                    formResult.textContent = ''; // Clear previous result
                    formResult.className = 'mt-4 text-sm'; // Reset class

                    const formData = new FormData(contactForm);

                    // Set timestamp for basic time-based spam detection
                    document.getElementById('form-timestamp').value = Date.now().toString();

                    // Check for rate limiting using localStorage
                    const lastSubmission = localStorage.getItem('lastFormSubmission');
                    const now = Date.now();
                    if (lastSubmission && (now - parseInt(lastSubmission)) < 60000) { // 1 minute cooldown
                        formResult.textContent = 'Please wait a minute before submitting again.';
                        formResult.classList.add('text-yellow-400');

                        // Warning animation
                        submitButton.classList.add('btn-error');
                        submitButton.style.animation = 'button-error 0.5s ease-in-out';

                        setTimeout(() => {
                            submitButton.style.animation = '';
                            submitButton.classList.remove('btn-error');
                            submitButton.disabled = false;
                            submitButton.textContent = 'Send Message';
                        }, 500);
                        return;
                    }

                    try {
                        console.log('Submitting form to Formcarry...');

                        // Log form data for debugging
                        for (let pair of formData.entries()) {
                            console.log(pair[0] + ': ' + pair[1]);
                        }

                        const response = await fetch(contactForm.action, {
                            method: 'POST',
                            body: formData,
                            headers: {
                                'Accept': 'application/json',
                                'X-Requested-With': 'XMLHttpRequest'
                            }
                        });

                        // Check if the response is OK
                        if (response.ok) {
                            // Store submission time for rate limiting
                            localStorage.setItem('lastFormSubmission', Date.now().toString());

                            // Success animation
                            submitButton.classList.add('btn-success');
                            submitButton.textContent = 'Sent!';

                            // Create success particles
                            for (let i = 0; i < 20; i++) {
                                setTimeout(() => {
                                    createParticle(submitButton);
                                }, i * 50);
                            }

                            // Success animation
                            submitButton.style.animation = 'button-success 0.5s ease-in-out';
                            setTimeout(() => {
                                submitButton.style.animation = '';
                            }, 500);

                            formResult.textContent = 'Message sent successfully!';
                            formResult.classList.add('text-green-400');

                            // Reset form
                            contactForm.reset();

                            // Close form after 2s on success
                            setTimeout(closeContactForm, 2000);
                        } else {
                            // Try to get JSON error message
                            let errorMessage = 'Form submission failed.';
                            try {
                                const errorData = await response.json();
                                errorMessage = errorData.error || errorMessage;
                            } catch (e) {
                                console.error('Could not parse error response:', e);
                            }

                            console.error("Form submission error:", errorMessage);

                            formResult.innerHTML = `
                                <div>Error: ${errorMessage}</div>
                                <div class="mt-2">
                                    <a href="mailto:<EMAIL>?subject=${encodeURIComponent('Contact from ' + formData.get('name'))}&body=${encodeURIComponent('Project: ' + formData.get('project') + '\n\n' + formData.get('message'))}"
                                       class="underline hover:text-blue-400">Try sending email directly</a>
                                </div>
                            `;

                            formResult.classList.add('text-red-400');

                            // Error animation
                            submitButton.classList.add('btn-error');
                            submitButton.style.animation = 'button-error 0.5s ease-in-out';

                            setTimeout(() => {
                                submitButton.style.animation = '';
                                submitButton.classList.remove('btn-error');
                                submitButton.disabled = false; // Re-enable button after animation
                                submitButton.textContent = 'Send Message';
                            }, 500);
                        }
                    } catch (error) {
                        console.error('Network or other error:', error);

                        formResult.innerHTML = `
                            <div>A network error occurred. Please try one of these options:</div>
                            <div class="mt-2">
                                <a href="mailto:<EMAIL>?subject=${encodeURIComponent('Contact from ' + formData.get('name'))}&body=${encodeURIComponent('Project: ' + formData.get('project') + '\n\n' + formData.get('message'))}"
                                   class="underline hover:text-blue-400">Send email directly</a>
                            </div>
                        `;

                        formResult.classList.add('text-red-400');

                        // Error animation
                        submitButton.classList.add('btn-error');
                        submitButton.style.animation = 'button-error 0.5s ease-in-out';

                        setTimeout(() => {
                            submitButton.style.animation = '';
                            submitButton.classList.remove('btn-error');
                            submitButton.disabled = false; // Re-enable button after animation
                            submitButton.textContent = 'Send Message';
                        }, 500);
                    }
                });
            }

            // --- Initialize ---
            // Profile orbs removed as requested

            // Removed contact form script as there's no form in the HTML
            /*
            document.getElementById('contactForm').addEventListener('submit', function(e) {
                // ... form submission logic ...
            });
            */

        }); // End DOMContentLoaded
    </script>

</body>
</html>