
<!DOCTYPE html>
<html lang="en">
<html oncontextmenu="return false" lang="en">
  <head>
    <title><PERSON></title>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">

    <link href="https://fonts.googleapis.com/css?family=Poppins:100,200,300,400,500,600,700,800,900" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css?family=Barlow+Condensed:900&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css?family=Lora:400,400i,700,700i&display=swap" rel="stylesheet">

    <link rel="stylesheet" href="css/open-iconic-bootstrap.min.css">
    <link rel="stylesheet" href="css/animate.css">
    <link rel="stylesheet" href="css/owl.carousel.min.css">
    <link rel="stylesheet" href="css/owl.theme.default.min.css">
    <link rel="stylesheet" href="css/magnific-popup.css">
    <link rel="stylesheet" href="css/aos.css">
    <link rel="stylesheet" href="css/ionicons.min.css">
    <link rel="stylesheet" href="css/bootstrap-datepicker.css">
    <link rel="stylesheet" href="css/jquery.timepicker.css">
    <link rel="stylesheet" href="css/flaticon.css">
    <link rel="stylesheet" href="css/icomoon.css">
    <link rel="stylesheet" href="css/style.css">

    <style>
      html, body {
        margin: 0;
        padding: 0;
        height: 100%;
        overflow: hidden;
        background: #000;
      }

      #loader {
        position: fixed;
        z-index: 9999;
        background: #000;
        width: 100vw;
        height: 100vh;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: opacity 1s ease, visibility 0.5s;
      }
      #loader.fade-out {
        opacity: 0;
        visibility: hidden;
      }
      .loader-spinner {
        border: 5px solid #f3f3f3;
        border-top: 5px solid #fff;
        border-radius: 50%;
        width: 60px;
        height: 60px;
        animation: spin 0.5s linear infinite;
      }
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
    </style>
  </head>
  <div onselectstart="return false;" onmousedown="return false;" style="-webkit-user-select: none; user-select: none;">
  <body>
    <!-- Loader -->
    <div id="loader">
      <div class="loader-spinner"></div>
    </div>

    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark ftco_navbar bg-dark ftco-navbar-light" id="ftco-navbar">
      <div class="container">
        <a class="navbar-brand" href="#">YK</a>
        <button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#ftco-nav"
          aria-controls="ftco-nav" aria-expanded="false" aria-label="Toggle navigation">
          <span class="oi oi-menu"></span> Menu
        </button>

        <div class="collapse navbar-collapse" id="ftco-nav">
          <ul class="navbar-nav ml-auto">
            <li class="nav-item"><a href="" class="nav-link"></a></li>
            <li class="nav-item"><a href="" class="nav-link"></a></li>
            <li class="nav-item"><a href="https://uzygram.zone.id" class="nav-link">ENTER</a></li>
          </ul>
        </div>
      </div>
    </nav>

    <!-- Hero Section -->
    <section id="home-section" class="hero">
      <h1 class="vr text-center">Yusuf</h1>
      <div class="js-fullheight home-wrap d-flex">
        <div class="half order-md-last"></div>
        <div class="half">
          <div class="home-slider owl-carousel">
            <div class="slider-item js-fullheight">
              <div class="overlay"></div>
              <div class="container-fluid p-0">
                <div class="row d-md-flex no-gutters slider-text js-fullheight align-items-center justify-content-end"
                  data-scrollax-parent="true">
                  <div class="one-third img js-fullheight" style="background-image:url(images/bg_1.jpg);"></div>
                </div>
              </div>
            </div>

            <div class="slider-item js-fullheight">
              <div class="overlay"></div>
              <div class="container-fluid p-0">
                <div class="row d-flex no-gutters slider-text js-fullheight align-items-center justify-content-end"
                  data-scrollax-parent="true">
                  <div class="one-third img js-fullheight" style="background-image:url(images/bg_2.jpg);">
                    <div class="overlay"></div>
                  </div>
                </div>
              </div>
            </div>

          </div>
        </div>
      </div>
    </section>

    <!-- Scripts -->
    <script src="js/jquery.min.js"></script>
    <script src="js/jquery-migrate-3.0.1.min.js"></script>
    <script src="js/popper.min.js"></script>
    <script src="js/bootstrap.min.js"></script>
    <script src="js/jquery.easing.1.3.js"></script>
    <script src="js/jquery.waypoints.min.js"></script>
    <script src="js/jquery.stellar.min.js"></script>
    <script src="js/owl.carousel.min.js"></script>
    <script src="js/jquery.magnific-popup.min.js"></script>
    <script src="js/aos.js"></script>
    <script src="js/jquery.animateNumber.min.js"></script>
    <script src="js/scrollax.min.js"></script>
    <script src="js/main.js"></script>

    <!-- Loader script -->
    <script>
      window.addEventListener('load', function () {
        const loader = document.getElementById('loader');
        loader.classList.add('fade-out');
      });
    </script>
  </body>
</html>
