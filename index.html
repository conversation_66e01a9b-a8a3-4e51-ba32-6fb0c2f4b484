
<!DOCTYPE html>
<html oncontextmenu="return false" lang="en">
  <head>
    <title><PERSON>.</title>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">

    <link href="https://fonts.googleapis.com/css?family=Poppins:100,200,300,400,500,600,700,800,900" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css?family=Barlow+Condensed:900&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css?family=Lora:400,400i,700,700i&display=swap" rel="stylesheet">

    <link rel="stylesheet" href="css/open-iconic-bootstrap.min.css">
    <link rel="stylesheet" href="css/animate.css">
    <link rel="stylesheet" href="css/owl.carousel.min.css">
    <link rel="stylesheet" href="css/owl.theme.default.min.css">
    <link rel="stylesheet" href="css/magnific-popup.css">
    <link rel="stylesheet" href="css/aos.css">
    <link rel="stylesheet" href="css/ionicons.min.css">
    <link rel="stylesheet" href="css/bootstrap-datepicker.css">
    <link rel="stylesheet" href="css/jquery.timepicker.css">
    <link rel="stylesheet" href="css/flaticon.css">
    <link rel="stylesheet" href="css/icomoon.css">
    <link rel="stylesheet" href="css/style.css">

    <style>
      html, body {
        margin: 0;
        padding: 0;
        height: 100%;
        overflow: hidden;
        background: #000;
      }
      .logo-container {
        position: relative;
        width: 100px;
        height: 100px;
      }
      .logo-image {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        object-fit: contain;
        opacity: 0;
        transition: opacity 0.5s ease-in-out;
      }
      .logo-image.active {
        opacity: 1;
      }

      #loader {
        position: fixed;
        z-index: 9999;
        background: #000;
        width: 100vw;
        height: 100vh;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: opacity 1s ease, visibility 0.5s;
      }
      #loader.fade-out {
        opacity: 0;
        visibility: hidden;
      }
      .loader-spinner {
        border: 5px solid #f3f3f3;
        border-top: 5px solid #fff;
        border-radius: 50%;
        width: 60px;
        height: 60px;
        animation: spin 1s linear infinite;
      }
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }

      /* --- Mobile responsiveness tweaks (without changing desktop) --- */
      .one-third.img { background-size: cover; background-position: center center; }

      /* Stack hero halves on tablets/phones */
      @media (max-width: 991.98px) {
        .home-wrap { flex-direction: column; }
        .home-wrap .half { width: 100%; }
      }

      /* Phone-specific adjustments */
      @media (max-width: 575.98px) {
        .logo-container { width: 56px; height: 56px; }
        /* Use dynamic viewport height on mobile to avoid URL-bar issues */
        .js-fullheight { height: 100dvh !important; min-height: 100dvh; height: -webkit-fill-available !important; }
        /* Improve loader fit and size on small phones */
        #loader { height: 100dvh; }
        .loader-spinner { width: 44px; height: 44px; border-width: 4px; border-top-width: 4px; }
        /* Increase tap targets */
        #ftco-navbar .navbar-nav .nav-link { padding-top: 1rem; padding-bottom: 1rem; font-size: 1rem; }
        #ftco-navbar .navbar-toggler { padding: .6rem .9rem; font-size: 18px; }
        /* Subtle outline weight for huge text */
        .hero h1.vr { -webkit-text-stroke-width: 0.6px; }
      }

      /* Small tablets */
      @media (min-width: 576px) and (max-width: 991.98px) {
        .logo-container { width: 72px; height: 72px; }
        /* Slightly larger tap targets on small tablets */
        #ftco-navbar .navbar-nav .nav-link { padding-top: 1rem; padding-bottom: 1rem; }
        #ftco-navbar .navbar-toggler { padding: .6rem .9rem; }
      }

      /* Keep desktop as designed */
      @media (min-width: 992px) {
        .logo-container { width: 100px; height: 100px; }
      }

      /* Minimal circle cursor (only on devices with a fine pointer, e.g., mouse) */
      @media (pointer: fine) {
        html, body { cursor: none; }
        .cursor-dot {
          position: fixed;
          top: 0;
          left: 0;
          width: 10px;
          height: 10px;
          border-radius: 50%;
          border: 1px solid rgba(255, 255, 255, 0.6);
          background: rgba(255, 255, 255, 0.06);
          pointer-events: none;
          z-index: 9998; /* below loader (9999) */
          transform: translate(-50%, -50%);
          transition: opacity 0.2s ease;
          opacity: 0; /* becomes visible when ready */
        }
        body.cursor-ready .cursor-dot { opacity: 1; }
      }
    </style>
  </head>
  <body>
  <div onselectstart="return false;" onmousedown="return false;" style="-webkit-user-select: none; user-select: none;">
    <!-- Loader -->
    <div id="loader">
      <div class="loader-spinner"></div>
    </div>

    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark ftco_navbar bg-dark ftco-navbar-light" id="ftco-navbar">
      <div class="container">
        <a class="navbar-brand" href="index.html">
          <div class="logo-container">
            <img src="yk.png" class="logo-image" id="logo1" alt="YK Logo 1">
            <img src="yk2.png" class="logo-image" id="logo2" alt="YK Logo 2">
          </div>
        </a>
        <button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#ftco-nav"
          aria-controls="ftco-nav" aria-expanded="false" aria-label="Toggle navigation">
          <span class="oi oi-menu"></span> Menu
        </button>

        <div class="collapse navbar-collapse" id="ftco-nav">
          <ul class="navbar-nav ml-auto">
            <li class="nav-item"><a href="dev/index.html" class="nav-link">ENTER</a></li>
          </ul>
        </div>
      </div>
    </nav>

    <!-- Hero Section -->
    <section id="home-section" class="hero">
      <h1 class="vr text-center">Yusuf</h1>
      <div class="js-fullheight home-wrap d-flex">
        <div class="half order-md-last"></div>
        <div class="half">
          <div class="home-slider owl-carousel">
            <div class="slider-item js-fullheight">
              <div class="overlay"></div>
              <div class="container-fluid p-0">
                <div class="row d-md-flex no-gutters slider-text js-fullheight align-items-center justify-content-end"
                  data-scrollax-parent="true">
                  <div class="one-third img js-fullheight" style="background-image:url(images/bg1.png);"></div>
                </div>
              </div>
            </div>

            <div class="slider-item js-fullheight">
              <div class="overlay"></div>
              <div class="container-fluid p-0">
                <div class="row d-flex no-gutters slider-text js-fullheight align-items-center justify-content-end"
                  data-scrollax-parent="true">
                  <div class="one-third img js-fullheight" style="background-image:url(images/bg2.png);">
                    <div class="overlay"></div>
                  </div>
                </div>
              </div>
            </div>

          </div>
        </div>
      </div>
    </section>

    <!-- Scripts -->
    <script src="js/jquery.min.js"></script>
    <script src="js/jquery-migrate-3.0.1.min.js"></script>
    <script src="js/popper.min.js"></script>
    <script src="js/bootstrap.min.js"></script>
    <script src="js/jquery.easing.1.3.js"></script>
    <script src="js/jquery.waypoints.min.js"></script>
    <script src="js/jquery.stellar.min.js"></script>
    <script src="js/owl.carousel.min.js"></script>
    <script src="js/jquery.magnific-popup.min.js"></script>
    <script src="js/aos.js"></script>
    <script src="js/jquery.animateNumber.min.js"></script>
    <script src="js/scrollax.min.js"></script>
    <script src="js/main.js"></script>

    <!-- Loader script -->
    <script>
      window.addEventListener('load', function () {
        const loader = document.getElementById('loader');
        loader.classList.add('fade-out');
        
        // Logo switching functionality
        const logo1 = document.getElementById('logo1');
        const logo2 = document.getElementById('logo2');
        const carousel = $('.owl-carousel');

        // Set initial state
        logo2.classList.add('active');

        carousel.on('changed.owl.carousel', function(event) {
            // Get current slide index
            const currentSlide = event.item.index;
            
            if (currentSlide % 2 === 0) {
                logo1.classList.remove('active');
                logo2.classList.add('active');
            } else {
                logo2.classList.remove('active');
                logo1.classList.add('active');
            }
        });
      });
    </script>
    <script>
      (function() {
        try {
          var mqFine = window.matchMedia && window.matchMedia('(pointer: fine)');
          if (!mqFine || !mqFine.matches) return; // Only enable for mouse/trackpad

          var dot = document.createElement('div');
          dot.className = 'cursor-dot';
          document.body.appendChild(dot);

          var targetX = window.innerWidth / 2;
          var targetY = window.innerHeight / 2;
          var currentX = targetX;
          var currentY = targetY;

          var mqReduce = window.matchMedia && window.matchMedia('(prefers-reduced-motion: reduce)');
          var reduced = mqReduce && mqReduce.matches;

          function lerp(a, b, n) { return a + (b - a) * n; }

          window.addEventListener('mousemove', function(e) {
            targetX = e.clientX;
            targetY = e.clientY;
          }, { passive: true });

          document.body.classList.add('cursor-ready');

          function tick() {
            var factor = reduced ? 1 : 0.18; // Smooth but subtle
            currentX = lerp(currentX, targetX, factor);
            currentY = lerp(currentY, targetY, factor);
            dot.style.left = currentX + 'px';
            dot.style.top = currentY + 'px';
            requestAnimationFrame(tick);
          }
          requestAnimationFrame(tick);

          document.addEventListener('mouseleave', function() { dot.style.opacity = '0'; });
          document.addEventListener('mouseenter', function() { dot.style.opacity = '1'; });
        } catch (e) {
          // Fail silently to avoid breaking the page
        }
      })();
    </script>
  </body>
</html>
