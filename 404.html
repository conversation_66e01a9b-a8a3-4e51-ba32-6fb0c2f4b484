<!DOCTYPE html>
<html lang="en">
<html oncontextmenu="return false" lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON>. - 404 ⚡</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        body {
            background: linear-gradient(135deg, #1a1a2e, #16213e);
            color: #fff;
            height: 100vh;
            overflow: hidden;
            display: flex;
            justify-content: center;
            align-items: center;
            perspective: 1000px;
        }
        
        .container {
            text-align: center;
            transform-style: preserve-3d;
            animation: floatAnimation 6s ease-in-out infinite;
        }
        
        .error-code {
            font-size: 10rem;
            font-weight: 900;
            line-height: 1;
            letter-spacing: -5px;
            color: transparent;
            -webkit-text-stroke: 2px #4cc9f0;
            position: relative;
            animation: glowAnimation 3s ease-in-out infinite alternate;
            text-shadow: 0 0 20px rgba(76, 201, 240, 0.6);
            margin-bottom: 1rem;
            transform-style: preserve-3d;
        }
        
        .error-text {
            font-size: 2rem;
            margin-bottom: 2rem;
            opacity: 0;
            animation: fadeIn 1s ease-out 0.5s forwards;
        }
        
        .error-message {
            font-size: 1.2rem;
            margin-bottom: 2rem;
            opacity: 0;
            animation: fadeIn 1s ease-out 1s forwards;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
        }
        
        .home-btn {
            display: inline-block;
            padding: 12px 30px;
            background: #4cc9f0;
            color: #0b132b;
            border: none;
            border-radius: 30px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            text-decoration: none;
            transition: all 0.3s ease;
            opacity: 0;
            animation: fadeIn 1s ease-out 1.5s forwards, pulseBtn 2s infinite 2.5s;
            position: relative;
            overflow: hidden;
            z-index: 1;
        }
        
        .home-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
            transition: 0.5s;
            z-index: -1;
        }
        
        .home-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 7px 20px rgba(76, 201, 240, 0.3);
        }
        
        .home-btn:hover::before {
            left: 100%;
            animation: shineEffect 1.5s infinite;
        }
        
        .particles {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
        }
        
        .particle {
            position: absolute;
            width: 8px;
            height: 8px;
            background: #ffffff;
            border-radius: 50%;
            pointer-events: none;
            opacity: 0;
        }
        
        @keyframes floatAnimation {
            0%, 100% { transform: translateY(0) rotateX(0); }
            50% { transform: translateY(-20px) rotateX(5deg); }
        }
        
        @keyframes glowAnimation {
            0% { text-shadow: 0 0 20px rgba(76, 201, 240, 0.6); }
            100% { text-shadow: 0 0 40px rgba(76, 201, 240, 0.8), 0 0 80px rgba(76, 201, 240, 0.4); }
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        @keyframes pulseBtn {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }
        
        @keyframes shineEffect {
            0% { left: -100%; }
            100% { left: 100%; }
        }
        
        @keyframes particleAnimation {
            0% { transform: translate(0, 0); opacity: 1; }
            100% { transform: translate(var(--x), var(--y)); opacity: 0; }
        }
        
        @keyframes codeGlitch {
            0% { transform: skewX(0deg); }
            20% { transform: skewX(10deg); }
            40% { transform: skewX(-10deg); }
            60% { transform: skewX(5deg); }
            80% { transform: skewX(-5deg); }
            100% { transform: skewX(0deg); }
        }
        
        .glitch {
            position: relative;
            animation: codeGlitch 5s infinite 3s;
        }
        
        .glitch::before,
        .glitch::after {
            content: "404";
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            opacity: 0.8;
        }
        
        .glitch::before {
            color: #ff006e;
            animation: glitch 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94) both infinite;
            animation-delay: 0.1s;
            z-index: -1;
        }
        
        .glitch::after {
            color: #00f5d4;
            animation: glitch 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94) reverse both infinite;
            animation-delay: 0.2s;
            z-index: -2;
        }
        
        @keyframes glitch {
            0% { transform: translate(0); }
            20% { transform: translate(-3px, 3px); }
            40% { transform: translate(-3px, -3px); }
            60% { transform: translate(3px, 3px); }
            80% { transform: translate(3px, -3px); }
            100% { transform: translate(0); }
        }
    </style>
</head>
<body>
    <div class="particles" id="particles"></div>
    <div class="container">
        <h1 class="error-code glitch">404</h1>
        <h2 class="error-text">Sorry, this page isn't available.</h2>
        <p class="error-message">This page has been removed or is temporarily unavailable atm - <b>Yusuf</b></p>
        <button onclick="location.href='https://yusuf.zone.id/'" type="button">‎ INSTANT TRANSMISSION‎‎ ‎ </button>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Create particles
            createParticles();
            
            // Simulate loading error
            simulateError();
            
            // Add event listeners
            document.getElementById('goHome').addEventListener('click', function(e) {
                e.preventDefault();
                simulateRedirect();
            });
        });

        function createParticles() {
            const particlesContainer = document.getElementById('particles');
            const particleCount = 50;
            
            for (let i = 0; i < particleCount; i++) {
                const particle = document.createElement('div');
                particle.className = 'particle';
                
                // Random positions
                const posX = Math.random() * window.innerWidth;
                const posY = Math.random() * window.innerHeight;
                
                // Random sizes
                const size = Math.random() * 5 + 3;
                
                // Random directions
                const moveX = (Math.random() - 0.5) * 200;
                const moveY = (Math.random() - 0.5) * 200;
                
                // Set styles
                particle.style.left = posX + 'px';
                particle.style.top = posY + 'px';
                particle.style.width = size + 'px';
                particle.style.height = size + 'px';
                particle.style.setProperty('--x', moveX + 'px');
                particle.style.setProperty('--y', moveY + 'px');
                
                // Add animation
                const animationDuration = Math.random() * 3 + 2;
                const animationDelay = Math.random() * 5;
                
                particle.style.animation = `particleAnimation ${animationDuration}s ease-out ${animationDelay}s infinite`;
                
                particlesContainer.appendChild(particle);
            }
        }

        function simulateError() {
            const errorCode = document.querySelector('.error-code');
            
            // Add some random glitches
            setInterval(() => {
                if (Math.random() > 0.7) {
                    errorCode.style.transform = `translateX(${(Math.random() - 0.5) * 10}px)`;
                    setTimeout(() => {
                        errorCode.style.transform = 'translateX(0)';
                    }, 100);
                }
            }, 2000);
        }

        function simulateRedirect() {
            const container = document.querySelector('.container');
            container.style.animation = 'none';
            container.style.transform = 'scale(0.8)';
            container.style.opacity = '0';
            container.style.transition = 'all 0.5s ease';
            
            setTimeout(() => {
                alert('This is a demo. In a real application, this would redirect to the homepage.');
                window.location.reload();
            }, 600);
        }

        // Add mouse move effect
        document.addEventListener('mousemove', function(e) {
            const container = document.querySelector('.container');
            const mouseX = (window.innerWidth / 2 - e.clientX) / 50;
            const mouseY = (window.innerHeight / 2 - e.clientY) / 50;
            
            container.style.transform = `rotateY(${mouseX}deg) rotateX(${-mouseY}deg)`;
        });
    </script>
</body>
</html>